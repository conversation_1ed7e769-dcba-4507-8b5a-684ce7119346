`timescale 1ns / 1ps

// ID/EX流水线寄存器
module reg_ID_EX (
    // 控制信号
    input  wire        clk,             // 时钟信号
    input  wire        rst,             // 复位信号
    input  wire        clear,           // 清除信号

    // ID阶段输入数据
    input  wire [31:0] id_rD1,          // ID阶段寄存器读数据1
    input  wire [31:0] id_rD2,          // ID阶段寄存器读数据2
    input  wire [31:0] id_pc,           // ID阶段PC
    input  wire [31:0] id_ext,          // ID阶段符号扩展结果
    input  wire [31:0] id_pc4,          // ID阶段PC+4
    input  wire [4:0]  id_wR,           // ID阶段写寄存器地址

    // ID阶段控制信号
    input  wire        id_rf_we,        // 寄存器写使能
    input  wire [1:0]  id_rf_wsel,      // 寄存器写数据选择
    input  wire        id_pc_sel,       // PC选择
    input  wire [1:0]  id_ram_wdin_op,  // 存储器写数据操作
    input  wire [2:0]  id_ram_rb_op,    // 存储器读数据操作
    input  wire        id_ram_we,       // 存储器写使能
    input  wire [1:0]  id_npc_op,       // NPC操作
    input  wire [3:0]  id_alu_op,       // ALU操作
    input  wire        id_alua_sel,     // ALU A端选择
    input  wire        id_alub_sel,     // ALU B端选择

    // 冒险处理信号
    input  wire        rs1_hazard,      // rs1冒险标志
    input  wire        rs2_hazard,      // rs2冒险标志
    input  wire [31:0] hazard_rD1,      // 冒险处理后的rD1
    input  wire [31:0] hazard_rD2,      // 冒险处理后的rD2

    // EX阶段输出数据
    output reg  [31:0] ex_rD1,          // EX阶段寄存器读数据1
    output reg  [31:0] ex_rD2,          // EX阶段寄存器读数据2
    output reg  [31:0] ex_pc,           // EX阶段PC
    output reg  [31:0] ex_ext,          // EX阶段符号扩展结果
    output reg  [31:0] ex_pc4,          // EX阶段PC+4
    output reg  [4:0]  ex_wR,           // EX阶段写寄存器地址

    // EX阶段控制信号
    output reg         ex_rf_we,        // 寄存器写使能
    output reg  [1:0]  ex_rf_wsel,      // 寄存器写数据选择
    output reg         ex_pc_sel,       // PC选择
    output reg  [1:0]  ex_ram_wdin_op,  // 存储器写数据操作
    output reg  [2:0]  ex_ram_rb_op,    // 存储器读数据操作
    output reg         ex_ram_we,       // 存储器写使能
    output reg  [1:0]  ex_npc_op,       // NPC操作
    output reg  [3:0]  ex_alu_op,       // ALU操作
    output reg         ex_alua_sel,     // ALU A端选择
    output reg         ex_alub_sel      // ALU B端选择
);

    // 流水线寄存器更新逻辑
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            // 复位时清零所有信号
            ex_rD1         <= 32'd0;
            ex_rD2         <= 32'd0;
            ex_pc          <= 32'd0;
            ex_ext         <= 32'd0;
            ex_pc4         <= 32'd0;
            ex_wR          <= 5'd0;
            ex_rf_we       <= 1'b0;
            ex_rf_wsel     <= 2'd0;
            ex_pc_sel      <= 1'b0;
            ex_ram_wdin_op <= 2'd0;
            ex_ram_rb_op   <= 3'd0;
            ex_ram_we      <= 1'b0;
            ex_npc_op      <= 2'd0;
            ex_alu_op      <= 4'd0;
            ex_alua_sel    <= 1'b0;
            ex_alub_sel    <= 1'b0;
        end else if (clear) begin
            // 清除时插入气泡（NOP）
            ex_rD1         <= 32'd0;
            ex_rD2         <= 32'd0;
            ex_pc          <= 32'd0;
            ex_ext         <= 32'd0;
            ex_pc4         <= 32'd0;
            ex_wR          <= 5'd0;
            ex_rf_we       <= 1'b0;
            ex_rf_wsel     <= 2'd0;
            ex_pc_sel      <= 1'b0;
            ex_ram_wdin_op <= 2'd0;
            ex_ram_rb_op   <= 3'd0;
            ex_ram_we      <= 1'b0;
            ex_npc_op      <= 2'd0;
            ex_alu_op      <= 4'd0;
            ex_alua_sel    <= 1'b0;
            ex_alub_sel    <= 1'b0;
        end else begin
            // 正常传递，考虑冒险处理
            ex_rD1         <= rs1_hazard ? hazard_rD1 : id_rD1;
            ex_rD2         <= rs2_hazard ? hazard_rD2 : id_rD2;
            ex_pc          <= id_pc;
            ex_ext         <= id_ext;
            ex_pc4         <= id_pc4;
            ex_wR          <= id_wR;
            ex_rf_we       <= id_rf_we;
            ex_rf_wsel     <= id_rf_wsel;
            ex_pc_sel      <= id_pc_sel;
            ex_ram_wdin_op <= id_ram_wdin_op;
            ex_ram_rb_op   <= id_ram_rb_op;
            ex_ram_we      <= id_ram_we;
            ex_npc_op      <= id_npc_op;
            ex_alu_op      <= id_alu_op;
            ex_alua_sel    <= id_alua_sel;
            ex_alub_sel    <= id_alub_sel;
        end
    end
endmodule
