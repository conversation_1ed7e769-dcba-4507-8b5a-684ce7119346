`timescale 1ns / 1ps
`include "defines.vh"

// 冒险检测和处理模块
module hazard (
    // ID阶段寄存器地址
    input  wire [4:0]  id_rR1,       // ID阶段rs1寄存器地址
    input  wire [4:0]  id_rR2,       // ID阶段rs2寄存器地址

    // EX阶段信号
    input  wire [4:0]  ex_wR,        // EX阶段写寄存器地址
    input  wire        ex_rf_we,     // EX阶段寄存器写使能
    input  wire [1:0]  ex_rf_wsel,   // EX阶段寄存器写数据选择
    input  wire [31:0] ex_C,         // EX阶段ALU结果
    input  wire [31:0] ex_ext,       // EX阶段符号扩展结果
    input  wire [31:0] ex_pc4,       // EX阶段PC+4

    // MEM阶段信号
    input  wire [4:0]  mem_wR,       // MEM阶段写寄存器地址
    input  wire        mem_rf_we,    // MEM阶段寄存器写使能
    input  wire [1:0]  mem_rf_wsel,  // MEM阶段寄存器写数据选择
    input  wire [31:0] mem_C,        // MEM阶段ALU结果
    input  wire [31:0] mem_ext,      // MEM阶段符号扩展结果
    input  wire [31:0] mem_pc4,      // MEM阶段PC+4
    input  wire [31:0] mem_rdo,      // MEM阶段存储器读数据

    // WB阶段信号
    input  wire [4:0]  wb_wR,        // WB阶段写寄存器地址
    input  wire        wb_rf_we,     // WB阶段寄存器写使能
    input  wire [1:0]  wb_rf_wsel,   // WB阶段寄存器写数据选择
    input  wire [31:0] wb_C,         // WB阶段ALU结果
    input  wire [31:0] wb_ext,       // WB阶段符号扩展结果
    input  wire [31:0] wb_pc4,       // WB阶段PC+4
    input  wire [31:0] wb_rdo,       // WB阶段存储器读数据

    // 输出信号
    output wire        stop,         // 流水线停止信号
    output wire        rs1_hazard,   // rs1冒险标志
    output wire        rs2_hazard,   // rs2冒险标志
    output reg  [31:0] hazard_rD1,   // 冒险处理后的rD1
    output reg  [31:0] hazard_rD2    // 冒险处理后的rD2
);

    // 冒险检测逻辑
    wire rs1_ID_EX_hazard  = ex_rf_we  & (|ex_wR)  & (id_rR1 == ex_wR);   // ID-EX rs1冒险
    wire rs2_ID_EX_hazard  = ex_rf_we  & (|ex_wR)  & (id_rR2 == ex_wR);   // ID-EX rs2冒险
    wire rs1_ID_MEM_hazard = mem_rf_we & (|mem_wR) & (id_rR1 == mem_wR);  // ID-MEM rs1冒险
    wire rs2_ID_MEM_hazard = mem_rf_we & (|mem_wR) & (id_rR2 == mem_wR);  // ID-MEM rs2冒险
    wire rs1_ID_WB_hazard  = wb_rf_we  & (|wb_wR)  & (id_rR1 == wb_wR);   // ID-WB rs1冒险
    wire rs2_ID_WB_hazard  = wb_rf_we  & (|wb_wR)  & (id_rR2 == wb_wR);   // ID-WB rs2冒险

    // 流水线停止条件：Load-Use冒险
    assign stop = (rs1_ID_EX_hazard | rs2_ID_EX_hazard) & (ex_rf_wsel == `WD_RAM);

    // 冒险标志：存在冒险且不需要停止流水线
    assign rs1_hazard = (rs1_ID_EX_hazard | rs1_ID_MEM_hazard | rs1_ID_WB_hazard) & ~stop;
    assign rs2_hazard = (rs2_ID_EX_hazard | rs2_ID_MEM_hazard | rs2_ID_WB_hazard) & ~stop;
    // rs1冒险数据前递逻辑
    always @(*) begin
        case (1'b1)  // 优先级：EX > MEM > WB
            rs1_ID_EX_hazard: begin
                case (ex_rf_wsel)
                    `WD_ALUC: begin
                        hazard_rD1 = ex_C;      // 从EX阶段ALU结果前递
                    end
                    `WD_EXT: begin
                        hazard_rD1 = ex_ext;    // 从EX阶段立即数前递
                    end
                    `WD_PC4: begin
                        hazard_rD1 = ex_pc4;    // 从EX阶段PC+4前递
                    end
                    default: begin
                        hazard_rD1 = 32'h0;
                    end
                endcase
            end
            rs1_ID_MEM_hazard: begin
                case (mem_rf_wsel)
                    `WD_ALUC: begin
                        hazard_rD1 = mem_C;     // 从MEM阶段ALU结果前递
                    end
                    `WD_RAM: begin
                        hazard_rD1 = mem_rdo;   // 从MEM阶段存储器数据前递
                    end
                    `WD_EXT: begin
                        hazard_rD1 = mem_ext;   // 从MEM阶段立即数前递
                    end
                    `WD_PC4: begin
                        hazard_rD1 = mem_pc4;   // 从MEM阶段PC+4前递
                    end
                    default: begin
                        hazard_rD1 = 32'h0;
                    end
                endcase
            end
            rs1_ID_WB_hazard: begin
                case (wb_rf_wsel)
                    `WD_ALUC: begin
                        hazard_rD1 = wb_C;      // 从WB阶段ALU结果前递
                    end
                    `WD_RAM: begin
                        hazard_rD1 = wb_rdo;    // 从WB阶段存储器数据前递
                    end
                    `WD_EXT: begin
                        hazard_rD1 = wb_ext;    // 从WB阶段立即数前递
                    end
                    `WD_PC4: begin
                        hazard_rD1 = wb_pc4;    // 从WB阶段PC+4前递
                    end
                    default: begin
                        hazard_rD1 = 32'h0;
                    end
                endcase
            end
            default: hazard_rD1 = 32'h0;
        endcase
    end
    // rs2冒险数据前递逻辑
    always @(*) begin
        case (1'b1)  // 优先级：EX > MEM > WB
            rs2_ID_EX_hazard: begin
                case (ex_rf_wsel)
                    `WD_ALUC: begin
                        hazard_rD2 = ex_C;      // 从EX阶段ALU结果前递
                    end
                    `WD_EXT: begin
                        hazard_rD2 = ex_ext;    // 从EX阶段立即数前递
                    end
                    `WD_PC4: begin
                        hazard_rD2 = ex_pc4;    // 从EX阶段PC+4前递
                    end
                    default: begin
                        hazard_rD2 = 32'h0;
                    end
                endcase
            end
            rs2_ID_MEM_hazard: begin
                case (mem_rf_wsel)
                    `WD_ALUC: begin
                        hazard_rD2 = mem_C;     // 从MEM阶段ALU结果前递
                    end
                    `WD_RAM: begin
                        hazard_rD2 = mem_rdo;   // 从MEM阶段存储器数据前递
                    end
                    `WD_EXT: begin
                        hazard_rD2 = mem_ext;   // 从MEM阶段立即数前递
                    end
                    `WD_PC4: begin
                        hazard_rD2 = mem_pc4;   // 从MEM阶段PC+4前递
                    end
                    default: begin
                        hazard_rD2 = 32'h0;
                    end
                endcase
            end
            rs2_ID_WB_hazard: begin
                case (wb_rf_wsel)
                    `WD_ALUC: begin
                        hazard_rD2 = wb_C;      // 从WB阶段ALU结果前递
                    end
                    `WD_RAM: begin
                        hazard_rD2 = wb_rdo;    // 从WB阶段存储器数据前递
                    end
                    `WD_EXT: begin
                        hazard_rD2 = wb_ext;    // 从WB阶段立即数前递
                    end
                    `WD_PC4: begin
                        hazard_rD2 = wb_pc4;    // 从WB阶段PC+4前递
                    end
                    default: begin
                        hazard_rD2 = 32'h0;
                    end
                endcase
            end
            default: hazard_rD2 = 32'h0;
        endcase
    end
endmodule
