#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Mon Jul 21 12:38:06 2025
# Process ID: 13240
# Current directory: H:/lab2/proj_miniRV_ego1/proj_pipeline
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent37500 H:\lab2\proj_miniRV_ego1\proj_pipeline\proj_single_cycle.xpr
# Log file: H:/lab2/proj_miniRV_ego1/proj_pipeline/vivado.log
# Journal file: H:/lab2/proj_miniRV_ego1/proj_pipeline\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.xpr
CRITICAL WARNING: [Project 1-19] Could not find the file 'C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci'.
Scanning sources...
Finished scanning sources
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
ERROR: [IP_Flow 19-993] Could not find IP file for IP 'cpuclk'.
CRITICAL WARNING: [IP_Flow 19-5097] Unable to determine VLNV from IP file; verify it has the correct syntax: 
ERROR: [Common 17-39] 'open_project' failed due to earlier errors.
update_compile_order -fileset sources_1
create_ip -name clk_wiz -vendor xilinx.com -library ip -version 6.0 -module_name clk_wiz_0 -dir h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.PRIMITIVE {PLL} CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.CLKOUT1_DRIVES {BUFG} CONFIG.CLKOUT2_DRIVES {BUFG} CONFIG.CLKOUT3_DRIVES {BUFG} CONFIG.CLKOUT4_DRIVES {BUFG} CONFIG.CLKOUT5_DRIVES {BUFG} CONFIG.CLKOUT6_DRIVES {BUFG} CONFIG.CLKOUT7_DRIVES {BUFG} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_COMPENSATION {ZHOLD} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips clk_wiz_0]
generate_target {instantiation_template} [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'clk_wiz_0'...
update_compile_order -fileset sources_1
export_ip_user_files -of_objects  [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xci] -no_script -reset -force -quiet
remove_files  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xci
export_ip_user_files -of_objects  [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -reset -force -quiet
remove_files  -fileset cpuclk C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci
INFO: [Project 1-386] Moving file 'C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci' from fileset 'cpuclk' to fileset 'sources_1'.
create_ip -name clk_wiz -vendor xilinx.com -library ip -version 6.0 -module_name clk_wiz_0 -dir h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.PRIMITIVE {PLL} CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.CLKOUT1_DRIVES {BUFG} CONFIG.CLKOUT2_DRIVES {BUFG} CONFIG.CLKOUT3_DRIVES {BUFG} CONFIG.CLKOUT4_DRIVES {BUFG} CONFIG.CLKOUT5_DRIVES {BUFG} CONFIG.CLKOUT6_DRIVES {BUFG} CONFIG.CLKOUT7_DRIVES {BUFG} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_COMPENSATION {ZHOLD} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips clk_wiz_0]
generate_target {instantiation_template} [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0_1/clk_wiz_0.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'clk_wiz_0'...
update_compile_order -fileset sources_1
export_ip_user_files -of_objects  [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0_1/clk_wiz_0.xci] -no_script -reset -force -quiet
remove_files  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0_1/clk_wiz_0.xci
create_ip -name clk_wiz -vendor xilinx.com -library ip -version 6.0 -module_name cpuclk -dir h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.Component_Name {cpuclk} CONFIG.PRIMITIVE {PLL} CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.CLKOUT1_DRIVES {BUFG} CONFIG.CLKOUT2_DRIVES {BUFG} CONFIG.CLKOUT3_DRIVES {BUFG} CONFIG.CLKOUT4_DRIVES {BUFG} CONFIG.CLKOUT5_DRIVES {BUFG} CONFIG.CLKOUT6_DRIVES {BUFG} CONFIG.CLKOUT7_DRIVES {BUFG} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_COMPENSATION {ZHOLD} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips cpuclk]
WARNING: [Vivado 12-3523] Attempt to change 'Component_Name' from 'cpuclk' to 'cpuclk' is not allowed and is ignored.
generate_target {instantiation_template} [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'cpuclk'...
generate_target all [get_files  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'cpuclk'...
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
launch_runs -jobs 32 cpuclk_synth_1
[Mon Jul 21 12:40:16 2025] Launched cpuclk_synth_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
export_simulation -of_objects [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
WARNING: [Vivado 12-1017] Problems encountered:
1. Failed to delete one or more files in run directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1

launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Mon Jul 21 12:40:24 2025] Launched cpuclk_synth_1, synth_1...
Run output will be captured here:
cpuclk_synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/runme.log
[Mon Jul 21 12:40:25 2025] Launched impl_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/runme.log
exit
INFO: [Common 17-206] Exiting Vivado at Mon Jul 21 12:49:45 2025...
