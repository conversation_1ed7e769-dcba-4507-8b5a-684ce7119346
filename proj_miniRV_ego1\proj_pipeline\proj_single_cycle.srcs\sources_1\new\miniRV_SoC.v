`timescale 1ns / 1ps
// `include "defines.vh"

module miniRV_SoC (
    input  wire         fpga_rst,  
    input  wire         fpga_clk,

    input  wire [15:0]  sw,
    input  wire [ 4:0]  button,
    output wire [ 7:0]  dig_en,

    output wire         DN_A0, DN_A1,
    output wire         DN_B0, DN_B1,
    output wire         DN_C0, DN_C1,
    output wire         DN_D0, DN_D1,
    output wire         DN_E0, DN_E1,
    output wire         DN_F0, DN_F1,
    output wire         DN_G0, DN_G1,
    output wire         DN_DP0, DN_DP1,
    output wire [15:0]  led

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value      
`endif
);

    wire        pll_lock;
    wire        pll_clk;
    wire        cpu_clk;

    // Interface between CPU and IROM
`ifdef RUN_TRACE
    wire [15:0] inst_addr;
`else
    wire [13:0] inst_addr;
`endif
    wire [31:0] inst;

    // Interface between CPU and Bridge
    wire [31:0] Bus_rdata;
    wire [31:0] Bus_addr;
    wire        Bus_we;
    wire [31:0] Bus_wdata;
    
    // Interface between bridge and DRAM
    // wire         rst_bridge2dram;
    wire         clk_bridge2dram;
    wire [31:0]  addr_bridge2dram;
    wire [31:0]  rdata_dram2bridge;
    wire         we_bridge2dram;
    wire [31:0]  wdata_bridge2dram;
    wire        rst_bridge2dig;
    wire        clk_bridge2dig;
    wire [31:0] addr_bridge2dig;
    wire        we_bridge2dig;
    wire [31:0] wdata_bridge2dig;
    
    wire        rst_bridge2led;
    wire        clk_bridge2led;
    wire [31:0] addr_bridge2led;
    wire        we_bridge2led;
    wire [31:0] wdata_bridge2led;
    
    wire        rst_bridge2sw;
    wire        clk_bridge2sw;
    wire [31:0] addr_bridge2sw;
    wire [31:0] rdata_sw2bridge;
    
    wire        rst_bridge2btn;
    wire        clk_bridge2btn;
    wire [31:0] addr_bridge2btn;
    wire [31:0] rdata_btn2bridge;

    wire        rst_bridge2tim;
    wire        clk_bridge2tim;
    wire [31:0] addr_bridge2tim;
    wire        we_bridge2tim;
    wire [31:0] wdata_bridge2tim;
    wire [31:0] rdata_tim2bridge;
    
`ifdef RUN_TRACE
    assign cpu_clk = fpga_clk;
`else
    assign cpu_clk = pll_clk & pll_lock;
    cpuclk Clkgen (
        // .resetn     (!fpga_rst),
        .clk_in1    (fpga_clk),
        .clk_out1   (pll_clk),
        .locked     (pll_lock)
    );
`endif
    
    myCPU Core_cpu (
        .cpu_rst            (fpga_rst),
        .cpu_clk            (cpu_clk),

        // Interface to IROM
        .inst_addr          (inst_addr),
        .inst               (inst),

        // Interface to Bridge
        .Bus_addr           (Bus_addr),
        .Bus_rdata          (Bus_rdata),
        .Bus_we             (Bus_we),
        .Bus_wdata          (Bus_wdata)

`ifdef RUN_TRACE
        ,// Debug Interface
        .debug_wb_have_inst (debug_wb_have_inst),
        .debug_wb_pc        (debug_wb_pc),
        .debug_wb_ena       (debug_wb_ena),
        .debug_wb_reg       (debug_wb_reg),
        .debug_wb_value     (debug_wb_value)
`endif
    );
    
    IROM Mem_IROM (
        .a          (inst_addr),
        .spo        (inst)
    );
    
    Bridge Bridge (       
        // Interface to CPU
        .rst_from_cpu       (fpga_rst),
        .clk_from_cpu       (cpu_clk),
        .addr_from_cpu      (Bus_addr),
        .we_from_cpu        (Bus_we),
        .wdata_from_cpu     (Bus_wdata),
        .rdata_to_cpu       (Bus_rdata),
        
        // Interface to DRAM
        // .rst_to_dram    (rst_bridge2dram),
        .clk_to_dram        (clk_bridge2dram),
        .addr_to_dram       (addr_bridge2dram),
        .rdata_from_dram    (rdata_dram2bridge),
        .we_to_dram         (we_bridge2dram),
        .wdata_to_dram      (wdata_bridge2dram),
        
        // Interface to 7-seg digital LEDs
        .rst_to_dig         (rst_bridge2dig),
        .clk_to_dig         (clk_bridge2dig),
        .addr_to_dig        (addr_bridge2dig),
        .we_to_dig          (we_bridge2dig),
        .wdata_to_dig       (wdata_bridge2dig),

        // Interface to LEDs
        .rst_to_led         (rst_bridge2led),
        .clk_to_led         (clk_bridge2led),
        .addr_to_led        (addr_bridge2led),
        .we_to_led          (we_bridge2led),
        .wdata_to_led       (wdata_bridge2led),

        // Interface to switches
        .rst_to_sw          (rst_bridge2sw),
        .clk_to_sw          (clk_bridge2sw),
        .addr_to_sw         (addr_bridge2sw),
        .rdata_from_sw      (rdata_sw2bridge),

        // Interface to buttons
        .rst_to_btn         (rst_bridge2btn),
        .clk_to_btn         (clk_bridge2btn),
        .addr_to_btn        (addr_bridge2btn),
        .rdata_from_btn     (rdata_btn2bridge),

        // Interface to timer
        .rst_to_tim         (rst_bridge2tim),
        .clk_to_tim         (clk_bridge2tim),
        .addr_to_tim        (addr_bridge2tim),
        .we_to_tim          (we_bridge2tim),
        .wdata_to_tim       (wdata_bridge2tim),
        .rdata_from_tim     (rdata_tim2bridge)
    );

    DRAM Mem_DRAM (
        .clk        (clk_bridge2dram),
        .a          (addr_bridge2dram[15:2]),
        .spo        (rdata_dram2bridge),
        .we         (we_bridge2dram),
        .d          (wdata_bridge2dram)
    );
    Dig U_dig (
        .rst(rst_bridge2dig),
        .clk(clk_bridge2dig),
        .addr(addr_bridge2dig),
        .we(we_bridge2dig),
        .wdata(wdata_bridge2dig),
        .dig_en(dig_en),
        .DN_A0(DN_A0), .DN_A1(DN_A1),
        .DN_B0(DN_B0), .DN_B1(DN_B1),
        .DN_C0(DN_C0), .DN_C1(DN_C1),
        .DN_D0(DN_D0), .DN_D1(DN_D1),
        .DN_E0(DN_E0), .DN_E1(DN_E1),
        .DN_F0(DN_F0), .DN_F1(DN_F1),
        .DN_G0(DN_G0), .DN_G1(DN_G1),
        .DN_DP0(DN_DP0), .DN_DP1(DN_DP1)
    );
    
    Led U_led (
        .rst(rst_bridge2led),
        .clk(clk_bridge2led),
        .addr(addr_bridge2led),
        .we(we_bridge2led),
        .wdata(wdata_bridge2led),  
        .led(led)
    );
    
    sw U_sw (
        .rst(rst_bridge2sw),
        .clk(clk_bridge2sw),
        .addr(addr_bridge2sw), 
        .sw(sw),
        .rdata(rdata_sw2bridge)
    );

    btn U_btn (
        .rst(rst_bridge2btn),
        .clk(clk_bridge2btn),
        .addr(addr_bridge2btn), 
        .button(button),
        .rdata(rdata_btn2bridge)
    );

    Timer U_Timer (
        .rst        (rst_bridge2tim),
        .clk        (clk_bridge2tim),
        .addr       (addr_bridge2tim),
        .we         (we_bridge2tim),
        .wdata      (wdata_bridge2tim),
        .rdata      (rdata_tim2bridge)
    );

endmodule