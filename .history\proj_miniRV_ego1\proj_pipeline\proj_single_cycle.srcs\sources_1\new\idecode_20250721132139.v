`timescale 1ns / 1ps
`include "defines.vh"

module idecode (
    // 指令信号
    input  wire [31:7] inst,      // 指令字段

    // 控制信号
    input  wire [2:0]  sext_op,   // 符号扩展操作
    input  wire        rf_we,     // 寄存器写使能
    input  wire [1:0]  rf_wsel,   // 寄存器写数据选择
    input  wire        clk,       // 时钟信号

    // 数据输入
    input  wire [31:0] ALUC,      // ALU计算结果
    input  wire [31:0] rdo,       // 存储器读数据
    input  wire [31:0] pc4,       // PC+4
    input  wire [4:0]  wR,        // 写寄存器地址
    input  wire [31:0] wb_ext,    // 写回扩展数据

    // 数据输出
    output wire [31:0] rD1,       // 寄存器读数据1
    output wire [31:0] rD2,       // 寄存器读数据2
    output wire [31:0] id_ext,    // 符号扩展结果
    output reg  [31:0] wD         // 写寄存器数据
);
    // 寄存器写数据选择
    always @(*) begin
        case (rf_wsel)
             `WD_ALUC: begin
                wD = ALUC;    // 选择ALU计算结果
             end
             `WD_RAM: begin
                wD = rdo;     // 选择存储器读数据
             end
             `WD_EXT: begin
                wD = wb_ext;  // 选择扩展立即数
             end
             `WD_PC4: begin
                wD = pc4;     // 选择PC+4
             end
             default: begin
                wD = 32'h0;
             end
        endcase
    end

    // 寄存器文件模块
    RF rf_module (
        .rR1(inst[19:15]),  // rs1寄存器地址
        .rR2(inst[24:20]),  // rs2寄存器地址
        .wR(wR),            // 写寄存器地址
        .we(rf_we),         // 写使能
        .clk(clk),          // 时钟
        .wD(wD),            // 写数据
        .rD1(rD1),          // 读数据1
        .rD2(rD2)           // 读数据2
    );

    // 符号扩展模块
    SEXT sext_module (
        .op(sext_op),       // 扩展操作
        .din(inst[31:7]),   // 输入数据
        .ext(id_ext)        // 扩展结果
    );
endmodule
