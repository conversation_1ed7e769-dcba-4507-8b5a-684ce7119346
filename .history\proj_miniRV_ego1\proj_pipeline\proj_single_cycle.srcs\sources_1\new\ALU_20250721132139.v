`timescale 1ns / 1ps
`include "defines.vh"

module ALU (
    // 控制信号
    input  wire [3:0]  op,    // ALU操作码
    // 数据信号
    input  wire [31:0] A,     // 操作数A
    input  wire [31:0] B,     // 操作数B

    output wire [31:0] C,     // 运算结果
    output wire        f      // 标志位输出
);

    reg [31:0] resultC;
    reg        resultf;

    assign C = resultC;
    assign f = resultf;
    


    /*
     * 在 RV-32I 中，实现移位指令时，B 有用的位数只有低五位
     * 详细见 RISC-V 手册
     */
    wire [4:0] shamt = B[4:0];

    always @(*) begin
        case (op)
            `ALU_ADD: begin
                resultC = A + B;
                resultf = 1'b0;
            end
            `ALU_SUB: begin
                resultC = A - B;
                resultf = 1'b0;
            end
            `ALU_AND: begin
                resultC = A & B;
                resultf = 1'b0;
            end
            `ALU_OR: begin
                resultC = A | B;
                resultf = 1'b0;
            end
            `ALU_XOR: begin
                resultC = A ^ B;
                resultf = 1'b0;
            end
            `ALU_SLL: begin
                resultC = A << shamt;
                resultf = 1'b0;
            end
            `ALU_SRL: begin
                resultC = A >> shamt;
                resultf = 1'b0;
            end
            `ALU_SRA: begin
                resultC = $signed(A) >>> shamt;  // 算术右移
                resultf = 1'b0;
            end
            `ALU_EQ: begin  // 相等比较
                resultC = 32'h0;
                resultf = (A == B);
            end
            `ALU_NE: begin  // 不等比较
                resultC = 32'h0;
                resultf = (A != B);
            end
            `ALU_LT: begin  // 有符号小于比较
                resultC = ($signed(A) < $signed(B));
                resultf = ($signed(A) < $signed(B));
            end
            `ALU_GE: begin  // 有符号大于等于比较
                resultC = ($signed(A) >= $signed(B));
                resultf = ($signed(A) >= $signed(B));
            end
            default: begin
                resultC = 32'h0;
                resultf = 1'b0;
            end
        endcase
    end
endmodule
