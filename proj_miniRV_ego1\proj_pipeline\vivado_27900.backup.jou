#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Wed Jul 16 13:09:07 2025
# Process ID: 27900
# Current directory: H:/lab2/proj_miniRV_ego1/proj_pipeline
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent7636 H:\lab2\proj_miniRV_ego1\proj_pipeline\proj_single_cycle.xpr
# Log file: H:/lab2/proj_miniRV_ego1/proj_pipeline/vivado.log
# Journal file: H:/lab2/proj_miniRV_ego1/proj_pipeline\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.xpr
update_compile_order -fileset sources_1
open_hw
connect_hw_server
open_hw_target
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
current_hw_device [get_hw_devices xc7a35t_0]
refresh_hw_device -update_hw_probes false [lindex [get_hw_devices xc7a35t_0] 0]
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
set_property -dict [list CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {60} CONFIG.MMCM_DIVCLK_DIVIDE {5} CONFIG.MMCM_CLKFBOUT_MULT_F {42} CONFIG.MMCM_CLKOUT0_DIVIDE_F {14} CONFIG.CLKOUT1_JITTER {313.062} CONFIG.CLKOUT1_PHASE_ERROR {310.955}] [get_ips cpuclk]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
reset_run cpuclk_synth_1
launch_runs -jobs 32 cpuclk_synth_1
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
set_property -dict [list CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips cpuclk]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
reset_run cpuclk_synth_1
launch_runs -jobs 32 cpuclk_synth_1
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
set_property -dict [list CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {100} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKOUT0_DIVIDE_F {9} CONFIG.CLKOUT1_JITTER {137.681}] [get_ips cpuclk]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
reset_run cpuclk_synth_1
launch_runs -jobs 32 cpuclk_synth_1
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
