#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Mon Jul 21 09:03:10 2025
# Process ID: 27748
# Current directory: H:/lab2/proj_miniRV_ego1/proj_pipeline
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent700 H:\lab2\proj_miniRV_ego1\proj_pipeline\proj_single_cycle.xpr
# Log file: H:/lab2/proj_miniRV_ego1/proj_pipeline/vivado.log
# Journal file: H:/lab2/proj_miniRV_ego1/proj_pipeline\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.xpr
CRITICAL WARNING: [Project 1-19] Could not find the file 'C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci'.
Scanning sources...
Finished scanning sources
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
ERROR: [IP_Flow 19-993] Could not find IP file for IP 'cpuclk'.
CRITICAL WARNING: [IP_Flow 19-5097] Unable to determine VLNV from IP file; verify it has the correct syntax: 
ERROR: [Common 17-39] 'open_project' failed due to earlier errors.
update_compile_order -fileset sources_1
exit
INFO: [Common 17-206] Exiting Vivado at Mon Jul 21 12:37:51 2025...
