`timescale 1ns / 1ps
`include "defines.vh"

module idecode (
    // 指令信号
    input  wire [31:7] inst,   

    // 控制信号
    input  wire [2:0]  sext_op,  
    input  wire        rf_we,    
    input  wire [1:0]  rf_wsel,  
    input  wire        clk,     

    // 数据输入
    input  wire [31:0] ALUC,     
    input  wire [31:0] rdo,       // 存储器读数据
    input  wire [31:0] pc4,  
    input  wire [4:0]  wR,        // 写寄存器地址
    input  wire [31:0] wb_ext,    // 写回扩展数据

    // 数据输出
    output wire [31:0] rD1,     
    output wire [31:0] rD2,     
    output wire [31:0] id_ext,    // 符号扩展结果
    output reg  [31:0] wD         // 写寄存器数据
);
    // 寄存器写数据选择
    always @(*) begin
        case (rf_wsel)
             `WD_ALUC: begin
                wD = ALUC;   
             end
             `WD_RAM: begin
                wD = rdo;     // 存储器读数据
             end
             `WD_EXT: begin
                wD = wb_ext;  // 扩展立即数
             end
             `WD_PC4: begin
                wD = pc4;     // PC+4
             end
             default: begin
                wD = 32'h0;
             end
        endcase
    end

    // 寄存器文件模块
    RF rf_module (
        .rR1(inst[19:15]),  
        .rR2(inst[24:20]),  
        .wR(wR),          
        .we(rf_we),        
        .clk(clk),       
        .wD(wD),           
        .rD1(rD1),         
        .rD2(rD2)      
    );

    // 符号扩展模块
    SEXT sext_module (
        .op(sext_op),      
        .din(inst[31:7]),  
        .ext(id_ext)     
    );
endmodule
