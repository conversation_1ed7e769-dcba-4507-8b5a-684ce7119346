`timescale 1ns / 1ps

module execute (
    // 输入数据
    input  wire [31:0] pc,       // PC值
    input  wire [31:0] rD1,      // 寄存器读数据1
    input  wire [31:0] ext,      // 符号扩展结果
    input  wire [31:0] rD2,      // 寄存器读数据2

    // 控制信号
    input  wire [3:0]  alu_op,   // ALU操作码
    input  wire        alua_sel, // ALU A端选择
    input  wire        alub_sel, // ALU B端选择

    // 输出信号
    output wire [31:0] C,        // ALU计算结果
    output wire        f         // ALU标志位
);

    // ALU输入选择
    wire [31:0] A;
    wire [31:0] B;
    assign A = alua_sel ? rD1 : pc;   // 选择寄存器值或PC
    assign B = alub_sel ? rD2 : ext;  // 选择寄存器值或立即数

    // ALU模块实例化
    ALU alu_module (
        .A(A),
        .B(B),
        .op(alu_op),
        .f(f),
        .C(C)
    );
endmodule
