// Annotate this macro before synthesis
`define RUN_TRACE

// ALU操作码定义
`define ALU_ADD  4'h0
`define ALU_SUB  4'h1
`define ALU_AND  4'h2
`define ALU_OR   4'h3
`define ALU_XOR  4'h4
`define ALU_SLL  4'h5
`define ALU_SRL  4'h6
`define ALU_SRA  4'h7
`define ALU_EQ   4'h8
`define ALU_NE   4'h9
`define ALU_LT   4'ha
`define ALU_GE   4'hb

// NPC操作码定义
`define NPC_PC4  2'h0
`define NPC_BEQ  2'h1
`define NPC_JMP  2'h2

// 寄存器写回数据选择
`define WD_ALUC  2'h0
`define WD_RAM   2'h1
`define WD_EXT   2'h2
`define WD_PC4   2'h3

// 符号扩展操作码
`define SEXT_I   3'h0
`define SEXT_S   3'h1
`define SEXT_B   3'h2
`define SEXT_U   3'h3
`define SEXT_J   3'h4

// 存储器操作码
`define WRAM_SW  2'h2
`define RDO_LW   3'h4

// 外设I/O接口电路的端口地址
`define PERI_ADDR_DIG   32'hFFFF_F000
`define PERI_ADDR_TIM0  32'hFFFF_F020
`define PERI_ADDR_TIM1  32'hFFFF_F024
`define PERI_ADDR_LED   32'hFFFF_F060
`define PERI_ADDR_SW    32'hFFFF_F070
`define PERI_ADDR_BTN   32'hFFFF_F078
