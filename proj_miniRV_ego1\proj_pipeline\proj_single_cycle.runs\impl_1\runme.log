
*** Running vivado
    with args -log miniRV_SoC.vdi -applog -m64 -product Vivado -messageDb vivado.pb -mode batch -source miniRV_SoC.tcl -notrace


****** Vivado v2018.3 (64-bit)
  **** SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
  **** IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

source miniRV_SoC.tcl -notrace
Command: link_design -top miniRV_SoC -part xc7a35tcsg324-1
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Project 1-454] Reading design checkpoint 'h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.dcp' for cell 'Clkgen'
INFO: [Project 1-454] Reading design checkpoint 'h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.dcp' for cell 'Mem_DRAM'
INFO: [Project 1-454] Reading design checkpoint 'h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.dcp' for cell 'Mem_IROM'
INFO: [Netlist 29-17] Analyzing 2537 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 1 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc] for cell 'Clkgen/inst'
Finished Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc] for cell 'Clkgen/inst'
Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc] for cell 'Clkgen/inst'
INFO: [Timing 38-35] Done setting XDC timing constraints. [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc:57]
INFO: [Timing 38-2] Deriving generated clocks [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc:57]
get_clocks: Time (s): cpu = 00:00:11 ; elapsed = 00:00:12 . Memory (MB): peak = 1296.445 ; gain = 574.098
Finished Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc] for cell 'Clkgen/inst'
Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
WARNING: [Constraints 18-619] A clock with name 'fpga_clk' already exists, overwriting the previous clock with the same name. [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc:2]
Finished Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
Finished Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.010 . Memory (MB): peak = 1299.156 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 2060 instances were transformed.
  RAM256X1S => RAM256X1S (MUXF7, MUXF7, MUXF8, RAMS64E, RAMS64E, RAMS64E, RAMS64E): 2048 instances
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 12 instances

12 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
link_design completed successfully
link_design: Time (s): cpu = 00:00:17 ; elapsed = 00:00:19 . Memory (MB): peak = 1299.156 ; gain = 942.066
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.544 . Memory (MB): peak = 1299.156 ; gain = 0.000

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: 1b35f216c

Time (s): cpu = 00:00:02 ; elapsed = 00:00:00.932 . Memory (MB): peak = 1307.496 ; gain = 8.340

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: d90632cd

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Opt 31-389] Phase Retarget created 0 cells and removed 1 cells
INFO: [Opt 31-1021] In phase Retarget, 1 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Phase 2 Constant propagation | Checksum: 1092244f4

Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Opt 31-389] Phase Constant propagation created 0 cells and removed 0 cells

Phase 3 Sweep
Phase 3 Sweep | Checksum: 15805481a

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 32 cells

Phase 4 BUFG optimization
INFO: [Opt 31-194] Inserted BUFG Clkgen/inst/clk_out1_cpuclk_BUFG_inst to drive 0 load(s) on clock net Clkgen/inst/clk_out1_cpuclk_BUFG
INFO: [Opt 31-193] Inserted 1 BUFG(s) on clock nets
Phase 4 BUFG optimization | Checksum: fbb2f20c

Time (s): cpu = 00:00:03 ; elapsed = 00:00:03 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: 15a6d35d2

Time (s): cpu = 00:00:03 ; elapsed = 00:00:04 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: 19e3bd900

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |               0  |               1  |                                              1  |
|  Constant propagation         |               0  |               0  |                                              0  |
|  Sweep                        |               0  |              32  |                                              0  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                              0  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.011 . Memory (MB): peak = 1395.734 ; gain = 0.000
Ending Logic Optimization Task | Checksum: 1e20fb916

Time (s): cpu = 00:00:04 ; elapsed = 00:00:04 . Memory (MB): peak = 1395.734 ; gain = 0.000

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
Ending Power Optimization Task | Checksum: 1e20fb916

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.022 . Memory (MB): peak = 1395.734 ; gain = 0.000

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: 1e20fb916

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1395.734 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.017 . Memory (MB): peak = 1395.734 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 1e20fb916

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.017 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
31 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:07 ; elapsed = 00:00:07 . Memory (MB): peak = 1395.734 ; gain = 96.578
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.010 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.032 . Memory (MB): peak = 1395.734 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.010 . Memory (MB): peak = 1395.734 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file miniRV_SoC_drc_opted.rpt -pb miniRV_SoC_drc_opted.pb -rpx miniRV_SoC_drc_opted.rpx
Command: report_drc -file miniRV_SoC_drc_opted.rpt -pb miniRV_SoC_drc_opted.pb -rpx miniRV_SoC_drc_opted.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1395.734 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: 12805ce1e

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.009 . Memory (MB): peak = 1395.734 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1395.734 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: 1c1949655

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1395.734 ; gain = 0.000

Phase 1.3 Build Placer Netlist Model
Phase 1.3 Build Placer Netlist Model | Checksum: 1ee989823

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 1ee989823

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1543.477 ; gain = 147.742
Phase 1 Placer Initialization | Checksum: 1ee989823

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 24113d7aa

Time (s): cpu = 00:00:06 ; elapsed = 00:00:05 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 2.2 Physical Synthesis In Placer
INFO: [Physopt 32-76] Pass 1. Identified 8 candidate nets for fanout optimization.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[3]. Replicated 12 times.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[2]. Replicated 11 times.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[5]. Replicated 12 times.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[4]. Replicated 12 times.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[6]. Replicated 12 times.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[8]. Replicated 11 times.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[7]. Replicated 11 times.
INFO: [Physopt 32-81] Processed net Core_cpu/U_EX_MEM/Q[9]. Replicated 11 times.
INFO: [Physopt 32-232] Optimized 8 nets. Created 92 new instances.
INFO: [Physopt 32-775] End 1 Pass. Optimized 8 nets or cells. Created 92 new cells, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.080 . Memory (MB): peak = 1543.477 ; gain = 0.000
INFO: [Physopt 32-456] No candidate cells for DSP register optimization found in the design.
INFO: [Physopt 32-775] End 2 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-677] No candidate cells for Shift Register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-526] No candidate cells for BRAM register optimization found in the design
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-949] No candidate nets found for HD net replication
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.012 . Memory (MB): peak = 1543.477 ; gain = 0.000

Summary of Physical Synthesis Optimizations
============================================


----------------------------------------------------------------------------------------------------------------------------------------
|  Optimization                  |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
----------------------------------------------------------------------------------------------------------------------------------------
|  Very High Fanout              |           92  |              0  |                     8  |           0  |           1  |  00:00:37  |
|  DSP Register                  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Shift Register                |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  BRAM Register                 |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  HD Interface Net Replication  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Total                         |           92  |              0  |                     8  |           0  |           5  |  00:00:38  |
----------------------------------------------------------------------------------------------------------------------------------------


Phase 2.2 Physical Synthesis In Placer | Checksum: 280b0bd98

Time (s): cpu = 00:01:07 ; elapsed = 00:01:08 . Memory (MB): peak = 1543.477 ; gain = 147.742
Phase 2 Global Placement | Checksum: 1c7b51acf

Time (s): cpu = 00:01:09 ; elapsed = 00:01:09 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 1c7b51acf

Time (s): cpu = 00:01:09 ; elapsed = 00:01:10 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 1a8a2f9f6

Time (s): cpu = 00:03:09 ; elapsed = 00:03:22 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 20874aea8

Time (s): cpu = 00:03:10 ; elapsed = 00:03:22 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 277c29c2a

Time (s): cpu = 00:03:10 ; elapsed = 00:03:22 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.5 Fast Optimization
Phase 3.5 Fast Optimization | Checksum: 256bf5b4c

Time (s): cpu = 00:03:16 ; elapsed = 00:03:28 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.6 Small Shape Detail Placement
Phase 3.6 Small Shape Detail Placement | Checksum: 25f8ba573

Time (s): cpu = 00:03:17 ; elapsed = 00:03:29 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.7 Re-assign LUT pins
Phase 3.7 Re-assign LUT pins | Checksum: 220e0e3af

Time (s): cpu = 00:03:17 ; elapsed = 00:03:29 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.8 Pipeline Register Optimization
Phase 3.8 Pipeline Register Optimization | Checksum: 1908ecb31

Time (s): cpu = 00:03:17 ; elapsed = 00:03:29 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 3.9 Fast Optimization
Phase 3.9 Fast Optimization | Checksum: 151a28cc6

Time (s): cpu = 00:03:23 ; elapsed = 00:03:35 . Memory (MB): peak = 1543.477 ; gain = 147.742
Phase 3 Detail Placement | Checksum: 151a28cc6

Time (s): cpu = 00:03:23 ; elapsed = 00:03:35 . Memory (MB): peak = 1543.477 ; gain = 147.742

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization
INFO: [Timing 38-35] Done setting XDC timing constraints.

Phase 4.1.1 Post Placement Optimization
Post Placement Optimization Initialization | Checksum: 14b6909c1

Phase ******* BUFG Insertion
INFO: [Place 46-46] BUFG insertion identified 0 candidate nets, 0 success, 0 bufg driver replicated, 0 skipped for placement/routing, 0 skipped for timing, 0 skipped for netlist change reason
Phase ******* BUFG Insertion | Checksum: 14b6909c1

Time (s): cpu = 00:03:28 ; elapsed = 00:03:38 . Memory (MB): peak = 1552.203 ; gain = 156.469
INFO: [Place 30-746] Post Placement Timing Summary WNS=0.511. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: 1923cacf3

Time (s): cpu = 00:03:40 ; elapsed = 00:03:50 . Memory (MB): peak = 1552.203 ; gain = 156.469
Phase 4.1 Post Commit Optimization | Checksum: 1923cacf3

Time (s): cpu = 00:03:40 ; elapsed = 00:03:50 . Memory (MB): peak = 1552.203 ; gain = 156.469

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 1923cacf3

Time (s): cpu = 00:03:40 ; elapsed = 00:03:51 . Memory (MB): peak = 1552.203 ; gain = 156.469

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 1923cacf3

Time (s): cpu = 00:03:40 ; elapsed = 00:03:51 . Memory (MB): peak = 1552.203 ; gain = 156.469

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1552.203 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 1490546c9

Time (s): cpu = 00:03:40 ; elapsed = 00:03:51 . Memory (MB): peak = 1552.203 ; gain = 156.469
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 1490546c9

Time (s): cpu = 00:03:40 ; elapsed = 00:03:51 . Memory (MB): peak = 1552.203 ; gain = 156.469
Ending Placer Task | Checksum: 62bc9434

Time (s): cpu = 00:03:40 ; elapsed = 00:03:51 . Memory (MB): peak = 1552.203 ; gain = 156.469
INFO: [Common 17-83] Releasing license: Implementation
69 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:03:41 ; elapsed = 00:03:52 . Memory (MB): peak = 1552.203 ; gain = 156.469
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1552.203 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 1552.203 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:02 ; elapsed = 00:00:00.616 . Memory (MB): peak = 1552.203 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file miniRV_SoC_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.032 . Memory (MB): peak = 1552.203 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file miniRV_SoC_utilization_placed.rpt -pb miniRV_SoC_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file miniRV_SoC_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.041 . Memory (MB): peak = 1552.203 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: b552522 ConstDB: 0 ShapeSum: 57676f12 RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 12d9b4805

Time (s): cpu = 00:00:11 ; elapsed = 00:00:09 . Memory (MB): peak = 1634.734 ; gain = 80.578
Post Restoration Checksum: NetGraph: 9d7a9bc1 NumContArr: 9020ac44 Constraints: 0 Timing: 0

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: 12d9b4805

Time (s): cpu = 00:00:11 ; elapsed = 00:00:09 . Memory (MB): peak = 1665.949 ; gain = 111.793

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: 12d9b4805

Time (s): cpu = 00:00:11 ; elapsed = 00:00:09 . Memory (MB): peak = 1672.594 ; gain = 118.438

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: 12d9b4805

Time (s): cpu = 00:00:11 ; elapsed = 00:00:09 . Memory (MB): peak = 1672.594 ; gain = 118.438
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: *********

Time (s): cpu = 00:00:17 ; elapsed = 00:00:13 . Memory (MB): peak = 1697.938 ; gain = 143.781
INFO: [Route 35-416] Intermediate Timing Summary | WNS=0.666  | TNS=0.000  | WHS=-0.244 | THS=-578.596|

Phase 2 Router Initialization | Checksum: 1f62a1324

Time (s): cpu = 00:00:18 ; elapsed = 00:00:14 . Memory (MB): peak = 1712.949 ; gain = 158.793

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: 1e770e2af

Time (s): cpu = 00:00:32 ; elapsed = 00:00:22 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 1190
 Number of Nodes with overlaps = 230
 Number of Nodes with overlaps = 64
 Number of Nodes with overlaps = 30
 Number of Nodes with overlaps = 17
 Number of Nodes with overlaps = 6
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=-0.092 | TNS=-0.438 | WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 22b2d5be8

Time (s): cpu = 00:00:50 ; elapsed = 00:00:35 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 4.2 Global Iteration 1
 Number of Nodes with overlaps = 258
 Number of Nodes with overlaps = 151
 Number of Nodes with overlaps = 92
 Number of Nodes with overlaps = 44
 Number of Nodes with overlaps = 34
 Number of Nodes with overlaps = 21
 Number of Nodes with overlaps = 7
 Number of Nodes with overlaps = 2
 Number of Nodes with overlaps = 2
 Number of Nodes with overlaps = 1
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=0.147  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 4.2 Global Iteration 1 | Checksum: 11779b3f0

Time (s): cpu = 00:01:00 ; elapsed = 00:00:42 . Memory (MB): peak = 1737.750 ; gain = 183.594
Phase 4 Rip-up And Reroute | Checksum: 11779b3f0

Time (s): cpu = 00:01:00 ; elapsed = 00:00:42 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp

Phase 5.1.1 Update Timing
Phase 5.1.1 Update Timing | Checksum: 1c73577ee

Time (s): cpu = 00:01:01 ; elapsed = 00:00:43 . Memory (MB): peak = 1737.750 ; gain = 183.594
INFO: [Route 35-416] Intermediate Timing Summary | WNS=0.147  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 5.1 Delay CleanUp | Checksum: 1c73577ee

Time (s): cpu = 00:01:01 ; elapsed = 00:00:43 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: 1c73577ee

Time (s): cpu = 00:01:01 ; elapsed = 00:00:43 . Memory (MB): peak = 1737.750 ; gain = 183.594
Phase 5 Delay and Skew Optimization | Checksum: 1c73577ee

Time (s): cpu = 00:01:01 ; elapsed = 00:00:43 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 1c8ad43f1

Time (s): cpu = 00:01:02 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594
INFO: [Route 35-416] Intermediate Timing Summary | WNS=0.147  | TNS=0.000  | WHS=0.067  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: 10d6bcace

Time (s): cpu = 00:01:02 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594
Phase 6 Post Hold Fix | Checksum: 10d6bcace

Time (s): cpu = 00:01:02 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 14.2656 %
  Global Horizontal Routing Utilization  = 10.6765 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Phase 7 Route finalize | Checksum: f8342b27

Time (s): cpu = 00:01:03 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: f8342b27

Time (s): cpu = 00:01:03 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: 130b96710

Time (s): cpu = 00:01:03 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=0.147  | TNS=0.000  | WHS=0.067  | THS=0.000  |

INFO: [Route 35-327] The final timing numbers are based on the router estimated timing analysis. For a complete and accurate timing signoff, please run report_timing_summary.
Phase 10 Post Router Timing | Checksum: 130b96710

Time (s): cpu = 00:01:03 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:01:03 ; elapsed = 00:00:44 . Memory (MB): peak = 1737.750 ; gain = 183.594

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
88 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:01:04 ; elapsed = 00:00:45 . Memory (MB): peak = 1737.750 ; gain = 185.547
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1737.750 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 1737.750 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:02 ; elapsed = 00:00:00.803 . Memory (MB): peak = 1737.750 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file miniRV_SoC_drc_routed.rpt -pb miniRV_SoC_drc_routed.pb -rpx miniRV_SoC_drc_routed.rpx
Command: report_drc -file miniRV_SoC_drc_routed.rpt -pb miniRV_SoC_drc_routed.pb -rpx miniRV_SoC_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file miniRV_SoC_methodology_drc_routed.rpt -pb miniRV_SoC_methodology_drc_routed.pb -rpx miniRV_SoC_methodology_drc_routed.rpx
Command: report_methodology -file miniRV_SoC_methodology_drc_routed.rpt -pb miniRV_SoC_methodology_drc_routed.pb -rpx miniRV_SoC_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC_methodology_drc_routed.rpt.
report_methodology completed successfully
report_methodology: Time (s): cpu = 00:00:09 ; elapsed = 00:00:05 . Memory (MB): peak = 1900.113 ; gain = 162.363
INFO: [runtcl-4] Executing : report_power -file miniRV_SoC_power_routed.rpt -pb miniRV_SoC_power_summary_routed.pb -rpx miniRV_SoC_power_routed.rpx
Command: report_power -file miniRV_SoC_power_routed.rpt -pb miniRV_SoC_power_summary_routed.pb -rpx miniRV_SoC_power_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
100 Infos, 1 Warnings, 0 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file miniRV_SoC_route_status.rpt -pb miniRV_SoC_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file miniRV_SoC_timing_summary_routed.rpt -pb miniRV_SoC_timing_summary_routed.pb -rpx miniRV_SoC_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
INFO: [runtcl-4] Executing : report_incremental_reuse -file miniRV_SoC_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file miniRV_SoC_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file miniRV_SoC_bus_skew_routed.rpt -pb miniRV_SoC_bus_skew_routed.pb -rpx miniRV_SoC_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -1, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
Command: write_bitstream -force miniRV_SoC.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7a35t'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC CFGBVS-1] Missing CFGBVS and CONFIG_VOLTAGE Design Properties: Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 1 Warnings
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Writing bitstream ./miniRV_SoC.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-120] WebTalk data collection is mandatory when using a WebPACK part without a full Vivado license. To see the specific WebTalk data collected for your design, open the usage_statistics_webtalk.html or usage_statistics_webtalk.xml file in the implementation directory.
INFO: [Common 17-83] Releasing license: Implementation
119 Infos, 2 Warnings, 0 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:14 ; elapsed = 00:00:12 . Memory (MB): peak = 2412.191 ; gain = 455.789
INFO: [Common 17-206] Exiting Vivado at Mon Jul 21 12:47:23 2025...
