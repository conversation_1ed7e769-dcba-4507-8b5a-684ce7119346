#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Mon Jul 21 12:40:37 2025
# Process ID: 40404
# Current directory: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1
# Command line: vivado.exe -log miniRV_SoC.vds -product Vivado -mode batch -messageDb vivado.pb -notrace -source miniRV_SoC.tcl
# Log file: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/miniRV_SoC.vds
# Journal file: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1\vivado.jou
#-----------------------------------------------------------
source miniRV_SoC.tcl -notrace
Command: synth_design -top miniRV_SoC -part xc7a35tcsg324-1
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7a35t'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 32052 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.984 . Memory (MB): peak = 469.195 ; gain = 101.781
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'miniRV_SoC' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/miniRV_SoC.v:4]
INFO: [Synth 8-6157] synthesizing module 'cpuclk' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/.Xil/Vivado-40404-L/realtime/cpuclk_stub.v:5]
INFO: [Synth 8-6155] done synthesizing module 'cpuclk' (1#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/.Xil/Vivado-40404-L/realtime/cpuclk_stub.v:5]
WARNING: [Synth 8-350] instance 'Clkgen' of module 'cpuclk' requires 4 connections, but only 3 given [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/miniRV_SoC.v:94]
INFO: [Synth 8-6157] synthesizing module 'myCPU' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/myCPU.v:4]
INFO: [Synth 8-6157] synthesizing module 'ifetch' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/ifetch.v:4]
INFO: [Synth 8-6157] synthesizing module 'NPC' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/NPC.v:4]
INFO: [Synth 8-6155] done synthesizing module 'NPC' (2#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/NPC.v:4]
INFO: [Synth 8-6157] synthesizing module 'PC' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/PC.v:3]
INFO: [Synth 8-6155] done synthesizing module 'PC' (3#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/PC.v:3]
INFO: [Synth 8-6155] done synthesizing module 'ifetch' (4#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/ifetch.v:4]
INFO: [Synth 8-6157] synthesizing module 'reg_IF_ID' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_IF_ID.v:3]
INFO: [Synth 8-6155] done synthesizing module 'reg_IF_ID' (5#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_IF_ID.v:3]
INFO: [Synth 8-6157] synthesizing module 'idecode' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/idecode.v:4]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/idecode.v:29]
INFO: [Synth 8-6157] synthesizing module 'RF' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/RF.v:3]
INFO: [Synth 8-6155] done synthesizing module 'RF' (6#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/RF.v:3]
INFO: [Synth 8-6157] synthesizing module 'SEXT' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/SEXT.v:4]
INFO: [Synth 8-6155] done synthesizing module 'SEXT' (7#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/SEXT.v:4]
INFO: [Synth 8-6155] done synthesizing module 'idecode' (8#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/idecode.v:4]
INFO: [Synth 8-6157] synthesizing module 'control' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/control.v:4]
INFO: [Synth 8-6155] done synthesizing module 'control' (9#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/control.v:4]
INFO: [Synth 8-6157] synthesizing module 'reg_ID_EX' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_ID_EX.v:4]
INFO: [Synth 8-6155] done synthesizing module 'reg_ID_EX' (10#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_ID_EX.v:4]
INFO: [Synth 8-6157] synthesizing module 'execute' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/execute.v:3]
INFO: [Synth 8-6157] synthesizing module 'ALU' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/ALU.v:4]
INFO: [Synth 8-6155] done synthesizing module 'ALU' (11#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/ALU.v:4]
INFO: [Synth 8-6155] done synthesizing module 'execute' (12#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/execute.v:3]
INFO: [Synth 8-6157] synthesizing module 'reg_EX_MEM' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_EX_MEM.v:4]
INFO: [Synth 8-6155] done synthesizing module 'reg_EX_MEM' (13#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_EX_MEM.v:4]
INFO: [Synth 8-6157] synthesizing module 'memory' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/memory.v:4]
INFO: [Synth 8-6155] done synthesizing module 'memory' (14#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/memory.v:4]
INFO: [Synth 8-6157] synthesizing module 'reg_MEM_WB' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_MEM_WB.v:4]
INFO: [Synth 8-6155] done synthesizing module 'reg_MEM_WB' (15#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/reg_MEM_WB.v:4]
INFO: [Synth 8-6157] synthesizing module 'hazard' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/hazard.v:4]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/hazard.v:78]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/hazard.v:97]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/hazard.v:139]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/hazard.v:158]
INFO: [Synth 8-6155] done synthesizing module 'hazard' (16#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/hazard.v:4]
INFO: [Synth 8-6155] done synthesizing module 'myCPU' (17#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/myCPU.v:4]
INFO: [Synth 8-6157] synthesizing module 'IROM' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/.Xil/Vivado-40404-L/realtime/IROM_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'IROM' (18#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/.Xil/Vivado-40404-L/realtime/IROM_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'Bridge' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Bridge.v:5]
INFO: [Synth 8-6155] done synthesizing module 'Bridge' (19#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Bridge.v:5]
INFO: [Synth 8-6157] synthesizing module 'DRAM' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/.Xil/Vivado-40404-L/realtime/DRAM_stub.v:6]
INFO: [Synth 8-6155] done synthesizing module 'DRAM' (20#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/.Xil/Vivado-40404-L/realtime/DRAM_stub.v:6]
INFO: [Synth 8-6157] synthesizing module 'Dig' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Dig.v:5]
INFO: [Synth 8-226] default block is never used [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Dig.v:49]
INFO: [Synth 8-6155] done synthesizing module 'Dig' (21#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Dig.v:5]
INFO: [Synth 8-6157] synthesizing module 'Led' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Led.v:5]
INFO: [Synth 8-6155] done synthesizing module 'Led' (22#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Led.v:5]
INFO: [Synth 8-6157] synthesizing module 'sw' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/sw.v:3]
INFO: [Synth 8-6155] done synthesizing module 'sw' (23#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/sw.v:3]
INFO: [Synth 8-6157] synthesizing module 'btn' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/btn.v:3]
INFO: [Synth 8-6155] done synthesizing module 'btn' (24#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/btn.v:3]
INFO: [Synth 8-6157] synthesizing module 'Timer' [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Timer.v:5]
INFO: [Synth 8-155] case statement is not full and has no default [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Timer.v:36]
INFO: [Synth 8-6155] done synthesizing module 'Timer' (25#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/Timer.v:5]
INFO: [Synth 8-6155] done synthesizing module 'miniRV_SoC' (26#1) [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/new/miniRV_SoC.v:4]
WARNING: [Synth 8-3331] design btn has unconnected port rst
WARNING: [Synth 8-3331] design btn has unconnected port clk
WARNING: [Synth 8-3331] design btn has unconnected port addr[31]
WARNING: [Synth 8-3331] design btn has unconnected port addr[30]
WARNING: [Synth 8-3331] design btn has unconnected port addr[29]
WARNING: [Synth 8-3331] design btn has unconnected port addr[28]
WARNING: [Synth 8-3331] design btn has unconnected port addr[27]
WARNING: [Synth 8-3331] design btn has unconnected port addr[26]
WARNING: [Synth 8-3331] design btn has unconnected port addr[25]
WARNING: [Synth 8-3331] design btn has unconnected port addr[24]
WARNING: [Synth 8-3331] design btn has unconnected port addr[23]
WARNING: [Synth 8-3331] design btn has unconnected port addr[22]
WARNING: [Synth 8-3331] design btn has unconnected port addr[21]
WARNING: [Synth 8-3331] design btn has unconnected port addr[20]
WARNING: [Synth 8-3331] design btn has unconnected port addr[19]
WARNING: [Synth 8-3331] design btn has unconnected port addr[18]
WARNING: [Synth 8-3331] design btn has unconnected port addr[17]
WARNING: [Synth 8-3331] design btn has unconnected port addr[16]
WARNING: [Synth 8-3331] design btn has unconnected port addr[15]
WARNING: [Synth 8-3331] design btn has unconnected port addr[14]
WARNING: [Synth 8-3331] design btn has unconnected port addr[13]
WARNING: [Synth 8-3331] design btn has unconnected port addr[12]
WARNING: [Synth 8-3331] design btn has unconnected port addr[11]
WARNING: [Synth 8-3331] design btn has unconnected port addr[10]
WARNING: [Synth 8-3331] design btn has unconnected port addr[9]
WARNING: [Synth 8-3331] design btn has unconnected port addr[8]
WARNING: [Synth 8-3331] design btn has unconnected port addr[7]
WARNING: [Synth 8-3331] design btn has unconnected port addr[6]
WARNING: [Synth 8-3331] design btn has unconnected port addr[5]
WARNING: [Synth 8-3331] design btn has unconnected port addr[4]
WARNING: [Synth 8-3331] design btn has unconnected port addr[3]
WARNING: [Synth 8-3331] design btn has unconnected port addr[2]
WARNING: [Synth 8-3331] design btn has unconnected port addr[1]
WARNING: [Synth 8-3331] design btn has unconnected port addr[0]
WARNING: [Synth 8-3331] design sw has unconnected port rst
WARNING: [Synth 8-3331] design sw has unconnected port clk
WARNING: [Synth 8-3331] design sw has unconnected port addr[31]
WARNING: [Synth 8-3331] design sw has unconnected port addr[30]
WARNING: [Synth 8-3331] design sw has unconnected port addr[29]
WARNING: [Synth 8-3331] design sw has unconnected port addr[28]
WARNING: [Synth 8-3331] design sw has unconnected port addr[27]
WARNING: [Synth 8-3331] design sw has unconnected port addr[26]
WARNING: [Synth 8-3331] design sw has unconnected port addr[25]
WARNING: [Synth 8-3331] design sw has unconnected port addr[24]
WARNING: [Synth 8-3331] design sw has unconnected port addr[23]
WARNING: [Synth 8-3331] design sw has unconnected port addr[22]
WARNING: [Synth 8-3331] design sw has unconnected port addr[21]
WARNING: [Synth 8-3331] design sw has unconnected port addr[20]
WARNING: [Synth 8-3331] design sw has unconnected port addr[19]
WARNING: [Synth 8-3331] design sw has unconnected port addr[18]
WARNING: [Synth 8-3331] design sw has unconnected port addr[17]
WARNING: [Synth 8-3331] design sw has unconnected port addr[16]
WARNING: [Synth 8-3331] design sw has unconnected port addr[15]
WARNING: [Synth 8-3331] design sw has unconnected port addr[14]
WARNING: [Synth 8-3331] design sw has unconnected port addr[13]
WARNING: [Synth 8-3331] design sw has unconnected port addr[12]
WARNING: [Synth 8-3331] design sw has unconnected port addr[11]
WARNING: [Synth 8-3331] design sw has unconnected port addr[10]
WARNING: [Synth 8-3331] design sw has unconnected port addr[9]
WARNING: [Synth 8-3331] design sw has unconnected port addr[8]
WARNING: [Synth 8-3331] design sw has unconnected port addr[7]
WARNING: [Synth 8-3331] design sw has unconnected port addr[6]
WARNING: [Synth 8-3331] design sw has unconnected port addr[5]
WARNING: [Synth 8-3331] design sw has unconnected port addr[4]
WARNING: [Synth 8-3331] design sw has unconnected port addr[3]
WARNING: [Synth 8-3331] design sw has unconnected port addr[2]
WARNING: [Synth 8-3331] design sw has unconnected port addr[1]
WARNING: [Synth 8-3331] design sw has unconnected port addr[0]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[31]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[30]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[29]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[28]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[27]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[26]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[25]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[24]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[23]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[22]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[21]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[20]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[19]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[18]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[17]
WARNING: [Synth 8-3331] design Led has unconnected port wdata[16]
WARNING: [Synth 8-3331] design memory has unconnected port ram_rb_op[2]
WARNING: [Synth 8-3331] design memory has unconnected port ram_rb_op[1]
WARNING: [Synth 8-3331] design memory has unconnected port ram_rb_op[0]
WARNING: [Synth 8-3331] design memory has unconnected port ram_wdin_op[1]
WARNING: [Synth 8-3331] design memory has unconnected port ram_wdin_op[0]
WARNING: [Synth 8-3331] design memory has unconnected port clk
WARNING: [Synth 8-3331] design control has unconnected port funct7[6]
WARNING: [Synth 8-3331] design control has unconnected port funct7[4]
WARNING: [Synth 8-3331] design control has unconnected port funct7[3]
WARNING: [Synth 8-3331] design control has unconnected port funct7[2]
WARNING: [Synth 8-3331] design control has unconnected port funct7[1]
WARNING: [Synth 8-3331] design control has unconnected port funct7[0]
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 525.234 ; gain = 157.820
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 525.234 ; gain = 157.820
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 525.234 ; gain = 157.820
---------------------------------------------------------------------------------
INFO: [Device 21-403] Loading part xc7a35tcsg324-1
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM/DRAM_in_context.xdc] for cell 'Mem_DRAM'
Finished Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM/DRAM_in_context.xdc] for cell 'Mem_DRAM'
Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/IROM/IROM/IROM_in_context.xdc] for cell 'Mem_IROM'
Finished Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/IROM/IROM/IROM_in_context.xdc] for cell 'Mem_IROM'
Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc] for cell 'Clkgen'
Finished Parsing XDC File [h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc] for cell 'Clkgen'
Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
WARNING: [Constraints 18-619] A clock with name 'fpga_clk' already exists, overwriting the previous clock with the same name. [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc:2]
Finished Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_clock.xdc]
Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
Finished Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/constrs_1/new/miniRV_SoC.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/miniRV_SoC_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/miniRV_SoC_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/dont_touch.xdc]
Finished Parsing XDC File [H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/dont_touch.xdc]
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 869.840 ; gain = 0.000
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 869.840 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 869.840 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 869.840 ; gain = 0.000
WARNING: [Timing 38-316] Clock period '20.000' specified during out-of-context synthesis of instance 'Mem_DRAM' at clock pin 'clk' is different from the actual clock period '11.111', this can lead to different synthesis results.
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 869.840 ; gain = 502.426
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7a35tcsg324-1
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 869.840 ; gain = 502.426
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
Applied set_property IO_BUFFER_TYPE = NONE for fpga_clk. (constraint file  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc, line 3).
Applied set_property CLOCK_BUFFER_TYPE = NONE for fpga_clk. (constraint file  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk/cpuclk_in_context.xdc, line 4).
Applied set_property DONT_TOUCH = true for Mem_DRAM. (constraint file  auto generated constraint, line ).
Applied set_property DONT_TOUCH = true for Mem_IROM. (constraint file  auto generated constraint, line ).
Applied set_property DONT_TOUCH = true for Clkgen. (constraint file  auto generated constraint, line ).
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:08 ; elapsed = 00:00:09 . Memory (MB): peak = 869.840 ; gain = 502.426
---------------------------------------------------------------------------------
INFO: [Synth 8-5546] ROM "ram_we0" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "pc_sel0" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "npc_op" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "alub_sel" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "sext_op" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "rf_wsel" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5587] ROM size for "rf_we" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5545] ROM "access_sw" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "access_btn" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "access_dig" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "access_led" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5546] ROM "rdata" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5545] ROM "threshold" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:08 ; elapsed = 00:00:10 . Memory (MB): peak = 869.840 ; gain = 502.426
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 5     
	   3 Input     32 Bit       Adders := 1     
	   2 Input      3 Bit       Adders := 1     
+---XORs : 
	   2 Input     32 Bit         XORs := 1     
+---Registers : 
	               32 Bit    Registers := 22    
	               16 Bit    Registers := 1     
	                5 Bit    Registers := 3     
	                4 Bit    Registers := 1     
	                3 Bit    Registers := 3     
	                2 Bit    Registers := 6     
	                1 Bit    Registers := 10    
+---RAMs : 
	              992 Bit         RAMs := 1     
+---Muxes : 
	   4 Input     32 Bit        Muxes := 7     
	   2 Input     32 Bit        Muxes := 20    
	   5 Input     32 Bit        Muxes := 1     
	   6 Input     32 Bit        Muxes := 1     
	   3 Input     32 Bit        Muxes := 2     
	   2 Input      5 Bit        Muxes := 1     
	   5 Input      4 Bit        Muxes := 1     
	   4 Input      4 Bit        Muxes := 1     
	   2 Input      4 Bit        Muxes := 1     
	   8 Input      4 Bit        Muxes := 1     
	   7 Input      3 Bit        Muxes := 2     
	   2 Input      3 Bit        Muxes := 3     
	   5 Input      3 Bit        Muxes := 1     
	   2 Input      2 Bit        Muxes := 4     
	   3 Input      2 Bit        Muxes := 1     
	   7 Input      2 Bit        Muxes := 1     
	   3 Input      1 Bit        Muxes := 4     
	   2 Input      1 Bit        Muxes := 14    
	   9 Input      1 Bit        Muxes := 1     
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
Module NPC 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 2     
+---Muxes : 
	   4 Input     32 Bit        Muxes := 1     
	   2 Input     32 Bit        Muxes := 2     
	   3 Input      1 Bit        Muxes := 1     
Module PC 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 1     
	                1 Bit    Registers := 1     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 1     
Module ifetch 
Detailed RTL Component Info : 
+---Muxes : 
	   2 Input     32 Bit        Muxes := 1     
Module reg_IF_ID 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 3     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 3     
	   2 Input      1 Bit        Muxes := 1     
Module RF 
Detailed RTL Component Info : 
+---RAMs : 
	              992 Bit         RAMs := 1     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 2     
Module idecode 
Detailed RTL Component Info : 
+---Muxes : 
	   4 Input     32 Bit        Muxes := 1     
Module control 
Detailed RTL Component Info : 
+---Muxes : 
	   5 Input      4 Bit        Muxes := 1     
	   4 Input      4 Bit        Muxes := 1     
	   7 Input      3 Bit        Muxes := 2     
	   2 Input      3 Bit        Muxes := 2     
	   5 Input      3 Bit        Muxes := 1     
	   2 Input      2 Bit        Muxes := 1     
	   3 Input      2 Bit        Muxes := 1     
	   7 Input      2 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 3     
	   3 Input      1 Bit        Muxes := 1     
	   9 Input      1 Bit        Muxes := 1     
Module reg_ID_EX 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 5     
	                5 Bit    Registers := 1     
	                4 Bit    Registers := 1     
	                3 Bit    Registers := 1     
	                2 Bit    Registers := 3     
	                1 Bit    Registers := 5     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 7     
	   2 Input      5 Bit        Muxes := 1     
	   2 Input      4 Bit        Muxes := 1     
	   2 Input      3 Bit        Muxes := 1     
	   2 Input      2 Bit        Muxes := 3     
Module ALU 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 1     
	   3 Input     32 Bit       Adders := 1     
+---XORs : 
	   2 Input     32 Bit         XORs := 1     
Module execute 
Detailed RTL Component Info : 
+---Muxes : 
	   2 Input     32 Bit        Muxes := 2     
Module reg_EX_MEM 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 5     
	                5 Bit    Registers := 1     
	                3 Bit    Registers := 1     
	                2 Bit    Registers := 2     
	                1 Bit    Registers := 2     
Module reg_MEM_WB 
Detailed RTL Component Info : 
+---Registers : 
	               32 Bit    Registers := 5     
	                5 Bit    Registers := 1     
	                2 Bit    Registers := 1     
	                1 Bit    Registers := 1     
Module hazard 
Detailed RTL Component Info : 
+---Muxes : 
	   4 Input     32 Bit        Muxes := 5     
Module Bridge 
Detailed RTL Component Info : 
+---Muxes : 
	   5 Input     32 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 4     
Module Dig 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input      3 Bit       Adders := 1     
+---Registers : 
	               32 Bit    Registers := 2     
	                3 Bit    Registers := 1     
	                1 Bit    Registers := 1     
+---Muxes : 
	   8 Input      4 Bit        Muxes := 1     
	   2 Input      1 Bit        Muxes := 3     
Module Led 
Detailed RTL Component Info : 
+---Registers : 
	               16 Bit    Registers := 1     
Module btn 
Detailed RTL Component Info : 
+---Muxes : 
	   6 Input     32 Bit        Muxes := 1     
Module Timer 
Detailed RTL Component Info : 
+---Adders : 
	   2 Input     32 Bit       Adders := 2     
+---Registers : 
	               32 Bit    Registers := 1     
+---Muxes : 
	   2 Input     32 Bit        Muxes := 2     
	   3 Input     32 Bit        Muxes := 2     
	   2 Input      1 Bit        Muxes := 3     
	   3 Input      1 Bit        Muxes := 2     
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 90 (col length:60)
BRAMs: 100 (col length: RAMB18 60 RAMB36 30)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
INFO: [Synth 8-5546] ROM "ram_we0" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "pc_sel0" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "alub_sel" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "sext_op" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5546] ROM "rf_wsel" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5587] ROM size for "rf_we" is below threshold of ROM address width. It will be mapped to LUTs
INFO: [Synth 8-5546] ROM "U_btn/rdata" won't be mapped to RAM because it is too sparse
INFO: [Synth 8-5545] ROM "Bridge/access_btn" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "Bridge/access_sw" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "Bridge/access_led" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
INFO: [Synth 8-5545] ROM "Bridge/access_dig" won't be mapped to RAM because address size (32) is larger than maximum supported(25)
WARNING: [Synth 8-3917] design miniRV_SoC has port DN_DP0 driven by constant 0
WARNING: [Synth 8-3917] design miniRV_SoC has port DN_DP1 driven by constant 0
WARNING: [Synth 8-3331] design control has unconnected port funct7[6]
WARNING: [Synth 8-3331] design control has unconnected port funct7[4]
WARNING: [Synth 8-3331] design control has unconnected port funct7[3]
WARNING: [Synth 8-3331] design control has unconnected port funct7[2]
INFO: [Common 17-14] Message 'Synth 8-3331' appears 100 times and further instances of the messages will be disabled. Use the Tcl command set_msg_config to change the current settings.
INFO: [Synth 8-3886] merging instance 'Core_cpu/U_IF_ID/id_pc4_reg[0]' (FDCE) to 'Core_cpu/U_IF_ID/id_pc_reg[0]'
INFO: [Synth 8-3886] merging instance 'Core_cpu/U_ID_EX/ex_pc4_reg[0]' (FDC) to 'Core_cpu/U_ID_EX/ex_pc_reg[0]'
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:42 ; elapsed = 00:00:46 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Distributed RAM: Preliminary Mapping  Report (see note below)
+------------+-------------------------------+-----------+----------------------+---------------+
|Module Name | RTL Object                    | Inference | Size (Depth x Width) | Primitives    | 
+------------+-------------------------------+-----------+----------------------+---------------+
|Core_cpu    | U_idecode/rf_module/regts_reg | Implied   | 32 x 32              | RAM32M x 12   | 
+------------+-------------------------------+-----------+----------------------+---------------+

Note: The table above is a preliminary report that shows the Distributed RAMs at the current stage of the synthesis flow. Some Distributed RAMs may be reimplemented as non Distributed RAM primitives later in the synthesis flow. Multiple instantiated RAMs are reported only once.
---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
INFO: [Synth 8-5578] Moved timing constraint from pin 'Clkgen/clk_out1' to pin 'Clkgen/bbstub_clk_out1/O'
WARNING: [Synth 8-565] redefining clock 'fpga_clk'
INFO: [Synth 8-5819] Moved 1 constraints on hierarchical pins to their respective driving/loading pins
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:50 ; elapsed = 00:00:54 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:52 ; elapsed = 00:00:56 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Distributed RAM: Final Mapping  Report
+------------+-------------------------------+-----------+----------------------+---------------+
|Module Name | RTL Object                    | Inference | Size (Depth x Width) | Primitives    | 
+------------+-------------------------------+-----------+----------------------+---------------+
|Core_cpu    | U_idecode/rf_module/regts_reg | Implied   | 32 x 32              | RAM32M x 12   | 
+------------+-------------------------------+-----------+----------------------+---------------+

---------------------------------------------------------------------------------
Finished ROM, RAM, DSP and Shift Register Reporting
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:53 ; elapsed = 00:00:58 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
CRITICAL WARNING: [Synth 8-4442] BlackBox module Clkgen has unconnected pin reset
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:54 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:54 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:54 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:54 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:54 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:54 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+------+--------------+----------+
|      |BlackBox name |Instances |
+------+--------------+----------+
|1     |cpuclk        |         1|
|2     |IROM          |         1|
|3     |DRAM          |         1|
+------+--------------+----------+

Report Cell Usage: 
+------+-------+------+
|      |Cell   |Count |
+------+-------+------+
|1     |DRAM   |     1|
|2     |IROM   |     1|
|3     |cpuclk |     1|
|4     |BUFG   |     1|
|5     |CARRY4 |    88|
|6     |LUT1   |    41|
|7     |LUT2   |   196|
|8     |LUT3   |   137|
|9     |LUT4   |   156|
|10    |LUT5   |   146|
|11    |LUT6   |   875|
|12    |MUXF7  |     4|
|13    |RAM32M |    12|
|14    |FDCE   |   785|
|15    |FDPE   |    13|
|16    |IBUF   |    22|
|17    |OBUF   |    40|
+------+-------+------+

Report Instance Areas: 
+------+-----------------+-----------+------+
|      |Instance         |Module     |Cells |
+------+-----------------+-----------+------+
|1     |top              |           |  2582|
|2     |  Core_cpu       |myCPU      |  2014|
|3     |    U_EX_MEM     |reg_EX_MEM |   446|
|4     |    U_ID_EX      |reg_ID_EX  |   983|
|5     |    U_IF_ID      |reg_IF_ID  |   298|
|6     |    U_MEM_WB     |reg_MEM_WB |   205|
|7     |    U_execute    |execute    |    20|
|8     |      alu_module |ALU        |    20|
|9     |    U_idecode    |idecode    |    12|
|10    |      rf_module  |RF         |    12|
|11    |    U_ifetch     |ifetch     |    50|
|12    |      npc_module |NPC        |    16|
|13    |      pc_module  |PC         |    34|
|14    |  U_Timer        |Timer      |   222|
|15    |  U_dig          |Dig        |   199|
|16    |  U_led          |Led        |    17|
+------+-----------------+-----------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:54 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 1 critical warnings and 9 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:50 ; elapsed = 00:00:55 . Memory (MB): peak = 940.926 ; gain = 228.906
Synthesis Optimization Complete : Time (s): cpu = 00:00:55 ; elapsed = 00:00:59 . Memory (MB): peak = 940.926 ; gain = 573.512
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 104 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 940.926 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 12 instances were transformed.
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 12 instances

INFO: [Common 17-83] Releasing license: Synthesis
100 Infos, 106 Warnings, 1 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:56 ; elapsed = 00:01:02 . Memory (MB): peak = 940.926 ; gain = 584.988
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.001 . Memory (MB): peak = 940.926 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/miniRV_SoC.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file miniRV_SoC_utilization_synth.rpt -pb miniRV_SoC_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Mon Jul 21 12:41:43 2025...
