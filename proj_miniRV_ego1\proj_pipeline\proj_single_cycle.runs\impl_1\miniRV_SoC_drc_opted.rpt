Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Jul 21 12:42:21 2025
| Host         : L running 64-bit major release  (build 9200)
| Command      : report_drc -file miniRV_SoC_drc_opted.rpt -pb miniRV_SoC_drc_opted.pb -rpx miniRV_SoC_drc_opted.rpx
| Design       : miniRV_SoC
| Device       : xc7a35tcsg324-1
| Speed File   : -1
| Design State : Synthesized
---------------------------------------------------------------------------------------------------------------------

Report DRC

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
           Ruledeck: default
             Max violations: <unlimited>
             Violations found: 1
+----------+----------+-----------------------------------------------------+------------+
| Rule     | Severity | Description                                         | Violations |
+----------+----------+-----------------------------------------------------+------------+
| CFGBVS-1 | Warning  | Missing CFGBVS and CONFIG_VOLTAGE Design Properties | 1          |
+----------+----------+-----------------------------------------------------+------------+

2. REPORT DETAILS
-----------------
CFGBVS-1#1 Warning
Missing CFGBVS and CONFIG_VOLTAGE Design Properties  
Neither the CFGBVS nor CONFIG_VOLTAGE voltage property is set in the current_design.  Configuration bank voltage select (CFGBVS) must be set to VCCO or GND, and CONFIG_VOLTAGE must be set to the correct configuration voltage, in order to determine the I/O voltage support for the pins in bank 0.  It is suggested to specify these either using the 'Edit Device Properties' function in the GUI or directly in the XDC file using the following syntax:

 set_property CFGBVS value1 [current_design]
 #where value1 is either VCCO or GND

 set_property CONFIG_VOLTAGE value2 [current_design]
 #where value2 is the voltage provided to configuration bank 0

Refer to the device configuration user guide for more information.
Related violations: <none>


