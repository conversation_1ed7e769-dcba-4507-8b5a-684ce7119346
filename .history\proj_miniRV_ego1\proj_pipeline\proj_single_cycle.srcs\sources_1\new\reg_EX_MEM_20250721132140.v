`timescale 1ns / 1ps

// EX/MEM流水线寄存器
module reg_EX_MEM (
    // 控制信号
    input  wire        clk,             // 时钟信号
    input  wire        rst,             // 复位信号

    // EX阶段输入数据
    input  wire [31:0] ex_C,            // EX阶段ALU计算结果
    input  wire [31:0] ex_pc4,          // EX阶段PC+4
    input  wire [31:0] ex_ext,          // EX阶段符号扩展结果
    input  wire [31:0] ex_rD2,          // EX阶段寄存器读数据2
    input  wire [31:0] ex_pc,           // EX阶段PC
    input  wire [4:0]  ex_wR,           // EX阶段写寄存器地址

    // EX阶段控制信号
    input  wire [1:0]  ex_ram_wdin_op,  // 存储器写数据操作
    input  wire [2:0]  ex_ram_rb_op,    // 存储器读数据操作
    input  wire        ex_ram_we,       // 存储器写使能
    input  wire        ex_rf_we,        // 寄存器写使能
    input  wire [1:0]  ex_rf_wsel,      // 寄存器写数据选择

    // MEM阶段输出数据
    output reg  [31:0] mem_C,           // MEM阶段ALU计算结果
    output reg  [31:0] mem_pc4,         // MEM阶段PC+4
    output reg  [31:0] mem_ext,         // MEM阶段符号扩展结果
    output reg  [31:0] mem_rD2,         // MEM阶段寄存器读数据2
    output reg  [31:0] mem_pc,          // MEM阶段PC
    output reg  [4:0]  mem_wR,          // MEM阶段写寄存器地址

    // MEM阶段控制信号
    output reg  [1:0]  mem_ram_wdin_op, // 存储器写数据操作
    output reg  [2:0]  mem_ram_rb_op,   // 存储器读数据操作
    output reg         mem_ram_we,      // 存储器写使能
    output reg         mem_rf_we,       // 寄存器写使能
    output reg  [1:0]  mem_rf_wsel      // 寄存器写数据选择
);

    // 流水线寄存器更新逻辑
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            // 复位时清零所有信号
            mem_C           <= 32'd0;
            mem_pc4         <= 32'd0;
            mem_ext         <= 32'd0;
            mem_rD2         <= 32'd0;
            mem_pc          <= 32'd0;
            mem_wR          <= 5'd0;
            mem_ram_wdin_op <= 2'd0;
            mem_ram_rb_op   <= 3'd0;
            mem_ram_we      <= 1'b0;
            mem_rf_we       <= 1'b0;
            mem_rf_wsel     <= 2'd0;
        end else begin
            // 正常传递EX阶段信号到MEM阶段
            mem_C           <= ex_C;
            mem_pc4         <= ex_pc4;
            mem_ext         <= ex_ext;
            mem_rD2         <= ex_rD2;
            mem_pc          <= ex_pc;
            mem_wR          <= ex_wR;
            mem_ram_wdin_op <= ex_ram_wdin_op;
            mem_ram_rb_op   <= ex_ram_rb_op;
            mem_ram_we      <= ex_ram_we;
            mem_rf_we       <= ex_rf_we;
            mem_rf_wsel     <= ex_rf_wsel;
        end
    end
endmodule
