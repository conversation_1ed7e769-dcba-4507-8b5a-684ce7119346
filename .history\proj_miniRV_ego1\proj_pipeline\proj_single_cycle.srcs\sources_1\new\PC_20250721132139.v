`timescale 1ns / 1ps

module PC (
    input  wire        rst,      // 复位信号
    input  wire        clk,      // 时钟信号
    input  wire [31:0] npc,      // 下一PC值
    output reg  [31:0] pc        // 当前PC值
);

    reg rst_s;  // 复位同步信号

    // PC寄存器更新逻辑
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            pc <= 32'h0;  // 异步复位到0
        end
        else if (rst_s) begin
            pc <= 32'h0;  // 同步复位到0
        end
        else begin
            pc <= npc;    // 更新为下一PC值
        end
    end

    // 复位同步逻辑
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            rst_s <= 1'b1;  // 复位时置位同步信号
        end
        else begin
            rst_s <= 1'b0;  // 正常运行时清除同步信号
        end
    end
endmodule
