`timescale 1ns / 1ps

module execute (
    // 输入数据
    input  wire [31:0] pc,      
    input  wire [31:0] rD1,     
    input  wire [31:0] ext,     
    input  wire [31:0] rD2,     

    // 控制信号
    input  wire [3:0]  alu_op,  
    input  wire        alua_sel, 
    input  wire        alub_sel, 

    // 输出信号
    output wire [31:0] C,        
    output wire        f         
);

    // ALU输入选择
    wire [31:0] A;
    wire [31:0] B;
    assign A = alua_sel ? rD1 : pc;   // 选择寄存器值或PC
    assign B = alub_sel ? rD2 : ext;  // 选择寄存器值或立即数

    // ALU模块实例化
    ALU alu_module (
        .A(A),
        .B(B),
        .op(alu_op),
        .f(f),
        .C(C)
    );
endmodule
