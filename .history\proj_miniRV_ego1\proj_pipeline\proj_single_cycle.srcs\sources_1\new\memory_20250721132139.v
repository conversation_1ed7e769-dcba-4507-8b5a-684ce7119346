`timescale 1ns / 1ps
`include "defines.vh"

module memory (
    // 控制信号
    input  wire [2:0]  ram_rb_op,    // 存储器读操作控制
    input  wire [1:0]  ram_wdin_op,  // 存储器写数据操作控制
    input  wire        ram_we,       // 存储器写使能
    input  wire        clk,          // 时钟信号

    // 数据信号
    input  wire [31:0] ALUC,         // ALU计算结果（地址）
    input  wire [31:0] din,          // 写入数据
    input  wire [31:0] Bus_rdata,    // 总线读取数据

    // 输出信号
    output reg  [31:0] rdo,          // 读取数据输出
    output wire        Bus_we,       // 总线写使能
    output wire [31:0] Bus_addr,     // 总线地址
    output reg  [31:0] Bus_wdata     // 总线写数据
);
    // 地址和写使能直接连接
    assign Bus_addr = ALUC;
    assign Bus_we = ram_we;

    // 写数据控制 - 仅支持SW指令
    always @(*) begin
        Bus_wdata = din;
    end

    // 读数据控制 - 仅支持LW指令
    always @(*) begin
        rdo = Bus_rdata;
    end
endmodule
