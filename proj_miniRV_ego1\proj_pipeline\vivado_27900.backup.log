#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Wed Jul 16 13:09:07 2025
# Process ID: 27900
# Current directory: H:/lab2/proj_miniRV_ego1/proj_pipeline
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent7636 H:\lab2\proj_miniRV_ego1\proj_pipeline\proj_single_cycle.xpr
# Log file: H:/lab2/proj_miniRV_ego1/proj_pipeline/vivado.log
# Journal file: H:/lab2/proj_miniRV_ego1/proj_pipeline\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.xpr
Scanning sources...
Finished scanning sources
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
open_project: Time (s): cpu = 00:00:12 ; elapsed = 00:00:06 . Memory (MB): peak = 860.066 ; gain = 167.844
update_compile_order -fileset sources_1
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
INFO: [Labtools 27-2222] Launching hw_server...
INFO: [Labtools 27-2221] Launch Output:

****** Xilinx hw_server v2018.3
  **** Build date : Dec  7 2018-00:40:27
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.


open_hw_target
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Xilinx/1234-tulA
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
current_hw_device [get_hw_devices xc7a35t_0]
refresh_hw_device -update_hw_probes false [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Wed Jul 16 13:15:47 2025] Launched synth_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/runme.log
[Wed Jul 16 13:15:47 2025] Launched impl_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/runme.log
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
set_property -dict [list CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {60} CONFIG.MMCM_DIVCLK_DIVIDE {5} CONFIG.MMCM_CLKFBOUT_MULT_F {42} CONFIG.MMCM_CLKOUT0_DIVIDE_F {14} CONFIG.CLKOUT1_JITTER {313.062} CONFIG.CLKOUT1_PHASE_ERROR {310.955}] [get_ips cpuclk]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'cpuclk'...
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
reset_run cpuclk_synth_1
launch_runs -jobs 32 cpuclk_synth_1
[Wed Jul 16 13:25:36 2025] Launched cpuclk_synth_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
WARNING: [Vivado 12-1017] Problems encountered:
1. Failed to delete one or more files in run directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1

launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Wed Jul 16 13:25:50 2025] Launched cpuclk_synth_1, synth_1...
Run output will be captured here:
cpuclk_synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/runme.log
[Wed Jul 16 13:25:50 2025] Launched impl_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/runme.log
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Wed Jul 16 13:31:33 2025] Launched synth_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/runme.log
[Wed Jul 16 13:31:33 2025] Launched impl_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/runme.log
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
set_property -dict [list CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips cpuclk]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'cpuclk'...
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
reset_run cpuclk_synth_1
launch_runs -jobs 32 cpuclk_synth_1
[Wed Jul 16 15:10:46 2025] Launched cpuclk_synth_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
WARNING: [Vivado 12-1017] Problems encountered:
1. Failed to delete one or more files in run directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1

launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Wed Jul 16 15:10:52 2025] Launched cpuclk_synth_1, synth_1...
Run output will be captured here:
cpuclk_synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/runme.log
[Wed Jul 16 15:10:52 2025] Launched impl_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/runme.log
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
set_property -dict [list CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {100} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKOUT0_DIVIDE_F {9} CONFIG.CLKOUT1_JITTER {137.681}] [get_ips cpuclk]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Implementation' target for IP 'cpuclk'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'cpuclk'...
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
reset_run cpuclk_synth_1
launch_runs -jobs 32 cpuclk_synth_1
[Wed Jul 16 15:36:30 2025] Launched cpuclk_synth_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
WARNING: [Vivado 12-1017] Problems encountered:
1. Failed to delete one or more files in run directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1

launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Wed Jul 16 15:36:37 2025] Launched cpuclk_synth_1, synth_1...
Run output will be captured here:
cpuclk_synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1/runme.log
synth_1: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1/runme.log
[Wed Jul 16 15:36:37 2025] Launched impl_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/runme.log
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
exit
INFO: [Common 17-206] Exiting Vivado at Wed Jul 16 16:07:57 2025...
