`timescale 1ns / 1ps

// MEM/WB流水线寄存器
module reg_MEM_WB (
    // 控制信号
    input  wire        clk,         // 时钟信号
    input  wire        rst,         // 复位信号

    // MEM阶段输入数据
    input  wire [31:0] mem_rdo,     // MEM阶段存储器读数据
    input  wire [31:0] mem_C,       // MEM阶段ALU计算结果
    input  wire [31:0] mem_pc4,     // MEM阶段PC+4
    input  wire [31:0] mem_ext,     // MEM阶段符号扩展结果
    input  wire [31:0] mem_pc,      // MEM阶段PC
    input  wire [4:0]  mem_wR,      // MEM阶段写寄存器地址

    // MEM阶段控制信号
    input  wire        mem_rf_we,   // 寄存器写使能
    input  wire [1:0]  mem_rf_wsel, // 寄存器写数据选择

    // WB阶段输出数据
    output reg  [31:0] wb_rdo,      // WB阶段存储器读数据
    output reg  [31:0] wb_C,        // WB阶段ALU计算结果
    output reg  [31:0] wb_pc4,      // WB阶段PC+4
    output reg  [31:0] wb_ext,      // WB阶段符号扩展结果
    output reg  [31:0] wb_pc,       // WB阶段PC
    output reg  [4:0]  wb_wR,       // WB阶段写寄存器地址

    // WB阶段控制信号
    output reg         wb_rf_we,    // 寄存器写使能
    output reg  [1:0]  wb_rf_wsel   // 寄存器写数据选择
);

    // 流水线寄存器更新逻辑
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            // 复位时清零所有信号
            wb_rdo     <= 32'd0;
            wb_C       <= 32'd0;
            wb_pc4     <= 32'd0;
            wb_ext     <= 32'd0;
            wb_pc      <= 32'd0;
            wb_wR      <= 5'd0;
            wb_rf_we   <= 1'b0;
            wb_rf_wsel <= 2'd0;
        end else begin
            // 正常传递MEM阶段信号到WB阶段
            wb_rdo     <= mem_rdo;
            wb_C       <= mem_C;
            wb_pc4     <= mem_pc4;
            wb_ext     <= mem_ext;
            wb_pc      <= mem_pc;
            wb_wR      <= mem_wR;
            wb_rf_we   <= mem_rf_we;
            wb_rf_wsel <= mem_rf_wsel;
        end
    end
endmodule
