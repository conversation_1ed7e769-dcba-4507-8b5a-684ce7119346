<?xml version="1.0"?>
<Runs Version="1" Minor="0">
	<Run Id="cpuclk_synth_1" LaunchDir="H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/cpuclk_synth_1" FlowId="Vivado_Synthesis" FromStepId="vivado" ToStepId="vivado"/>
	<Run Id="synth_1" LaunchDir="H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/synth_1" FlowId="Vivado_Synthesis" FromStepId="vivado" ToStepId="vivado">
		<Parent Id="cpuclk_synth_1"/>
	</Run>
	<Run Id="impl_1" LaunchDir="H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.runs/impl_1" FlowId="Vivado_Implementation" FromStepId="init_design" ToStepId="write_bitstream">
		<Parent Id="synth_1"/>
		<Parent Id="cpuclk_synth_1"/>
	</Run>
	<Parameters>
		<Parameter Name="runs.monitorLSFJobs" Val="true" Type="bool"/>
	</Parameters>
</Runs>

