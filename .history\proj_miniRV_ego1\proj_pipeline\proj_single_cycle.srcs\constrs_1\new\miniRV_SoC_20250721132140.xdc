set_property -dict { IOSTA<PERSON>ARD LVCMOS33 PACKAGE_PIN P15 } [get_ports fpga_rst]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN P17 } [get_ports fpga_clk]

set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN P5 } [get_ports sw[15]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN P4 } [get_ports sw[14]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN P3 } [get_ports sw[13]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN P2 } [get_ports sw[12]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN R2 } [get_ports sw[11]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN M4 } [get_ports sw[10]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN N4 } [get_ports sw[ 9]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN R1 } [get_ports sw[ 8]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN U3 } [get_ports sw[ 7]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN U2 } [get_ports sw[ 6]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN V2 } [get_ports sw[ 5]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN V5 } [get_ports sw[ 4]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN V4 } [get_ports sw[ 3]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN R3 } [get_ports sw[ 2]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN T3 } [get_ports sw[ 1]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN T5 } [get_ports sw[ 0]]

set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN U4  } [get_ports button[4]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN V1  } [get_ports button[3]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN R15 } [get_ports button[2]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN R17 } [get_ports button[1]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN R11 } [get_ports button[0]]

set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN G2 } [get_ports dig_en[7]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN C2 } [get_ports dig_en[6]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN C1 } [get_ports dig_en[5]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN H1 } [get_ports dig_en[4]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN G1 } [get_ports dig_en[3]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN F1 } [get_ports dig_en[2]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN E1 } [get_ports dig_en[1]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN G6 } [get_ports dig_en[0]]

set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN B4 } [get_ports DN_A0]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN A4 } [get_ports DN_B0]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN A3 } [get_ports DN_C0]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN B1 } [get_ports DN_D0]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN A1 } [get_ports DN_E0]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN B3 } [get_ports DN_F0]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN B2 } [get_ports DN_G0]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN D5 } [get_ports DN_DP0]

set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN D4 } [get_ports DN_A1]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN E3 } [get_ports DN_B1]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN D3 } [get_ports DN_C1]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN F4 } [get_ports DN_D1]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN F3 } [get_ports DN_E1]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN E2 } [get_ports DN_F1]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN D2 } [get_ports DN_G1]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN H2 } [get_ports DN_DP1]

set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN F6 } [get_ports led[15]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN G4 } [get_ports led[14]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN G3 } [get_ports led[13]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN J4 } [get_ports led[12]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN H4 } [get_ports led[11]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN J3 } [get_ports led[10]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN J2 } [get_ports led[ 9]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN K2 } [get_ports led[ 8]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN K1 } [get_ports led[ 7]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN H6 } [get_ports led[ 6]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN H5 } [get_ports led[ 5]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN J5 } [get_ports led[ 4]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN K6 } [get_ports led[ 3]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN L1 } [get_ports led[ 2]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN M1 } [get_ports led[ 1]]
set_property -dict { IOSTANDARD LVCMOS33 PACKAGE_PIN K3 } [get_ports led[ 0]]
