# This file is automatically generated.
# It contains project source information necessary for synthesis and implementation.

# IP: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci
# IP: The module: 'cpuclk' is the root of the design. Do not add the DONT_TOUCH constraint.

# XDC: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc
# XDC: The top module name and the constraint reference have the same name: 'cpuclk'. Do not add the DONT_TOUCH constraint.
set_property DONT_TOUCH TRUE [get_cells inst -quiet] -quiet

# XDC: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc
# XDC: The top module name and the constraint reference have the same name: 'cpuclk'. Do not add the DONT_TOUCH constraint.
#dup# set_property DONT_TOUCH TRUE [get_cells inst -quiet] -quiet

# XDC: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_ooc.xdc
# XDC: The top module name and the constraint reference have the same name: 'cpuclk'. Do not add the DONT_TOUCH constraint.
#dup# set_property DONT_TOUCH TRUE [get_cells inst -quiet] -quiet

# IP: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci
# IP: The module: 'cpuclk' is the root of the design. Do not add the DONT_TOUCH constraint.

# XDC: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_board.xdc
# XDC: The top module name and the constraint reference have the same name: 'cpuclk'. Do not add the DONT_TOUCH constraint.
#dup# set_property DONT_TOUCH TRUE [get_cells inst -quiet] -quiet

# XDC: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xdc
# XDC: The top module name and the constraint reference have the same name: 'cpuclk'. Do not add the DONT_TOUCH constraint.
#dup# set_property DONT_TOUCH TRUE [get_cells inst -quiet] -quiet

# XDC: h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk_ooc.xdc
# XDC: The top module name and the constraint reference have the same name: 'cpuclk'. Do not add the DONT_TOUCH constraint.
#dup# set_property DONT_TOUCH TRUE [get_cells inst -quiet] -quiet
