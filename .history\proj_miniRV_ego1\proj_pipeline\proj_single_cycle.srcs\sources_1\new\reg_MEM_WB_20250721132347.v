`timescale 1ns / 1ps

// MEM/WB流水线寄存器
module reg_MEM_WB (
    // 控制信号
    input  wire        clk,       
    input  wire        rst,       

    // MEM阶段输入数据
    input  wire [31:0] mem_rdo,     // MEM阶段存储器读数据
    input  wire [31:0] mem_C,      
    input  wire [31:0] mem_pc4,    
    input  wire [31:0] mem_ext,   
    input  wire [31:0] mem_pc,    
    input  wire [4:0]  mem_wR,      

    // MEM阶段控制信号
    input  wire        mem_rf_we,   
    input  wire [1:0]  mem_rf_wsel, 

    // WB阶段输出数据
    output reg  [31:0] wb_rdo,      
    output reg  [31:0] wb_C,        
    output reg  [31:0] wb_pc4,      
    output reg  [31:0] wb_ext,      
    output reg  [31:0] wb_pc,       
    output reg  [4:0]  wb_wR,       

    // WB阶段控制信号
    output reg         wb_rf_we,    
    output reg  [1:0]  wb_rf_wsel   
);

    // 流水线寄存器更新逻辑
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            // 复位
            wb_rdo     <= 32'd0;
            wb_C       <= 32'd0;
            wb_pc4     <= 32'd0;
            wb_ext     <= 32'd0;
            wb_pc      <= 32'd0;
            wb_wR      <= 5'd0;
            wb_rf_we   <= 1'b0;
            wb_rf_wsel <= 2'd0;
        end else begin
            // 正常传递
            wb_rdo     <= mem_rdo;
            wb_C       <= mem_C;
            wb_pc4     <= mem_pc4;
            wb_ext     <= mem_ext;
            wb_pc      <= mem_pc;
            wb_wR      <= mem_wR;
            wb_rf_we   <= mem_rf_we;
            wb_rf_wsel <= mem_rf_wsel;
        end
    end
endmodule
