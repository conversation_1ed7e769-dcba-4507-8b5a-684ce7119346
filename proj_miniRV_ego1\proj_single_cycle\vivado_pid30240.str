/*

Xilinx Vivado v2018.3 (64-bit) [Major: 2018, Minor: 3]
SW Build: 2405991 on Thu Dec  6 23:38:27 MST 2018
IP Build: 2404404 on Fri Dec  7 01:43:56 MST 2018

Process ID (PID): 30240
License: Customer

Current time: 	Mon Jul 21 09:03:16 CST 2025
Time zone: 	China Standard Time (Asia/Shanghai)

OS: Windows 10
OS Version: 10.0
OS Architecture: amd64
Available processors (cores): 32

Screen size: 2560x1600
Screen resolution (DPI): 144
Available screens: 1
Available disk space: 183 GB
Default font: family=Dialog,name=Dialog,style=plain,size=18

Java version: 	9.0.4 64-bit
Java home: 	E:/Vivado/2018.3/tps/win64/jre9.0.4
Java executable location: 	E:/Vivado/2018.3/tps/win64/jre9.0.4/bin/java.exe
Java initial memory (-Xms): 	128 MB
Java maximum memory (-Xmx):	 3 GB


User name: 	12460
User home directory: C:/Users/<USER>
User working directory: H:/lab2/proj_miniRV_ego1/proj_single_cycle
User country: 	CN
User language: 	zh
User locale: 	zh_CN

RDI_BASEROOT: E:/Vivado
HDI_APPROOT: E:/Vivado/2018.3
RDI_DATADIR: E:/Vivado/2018.3/data
RDI_BINDIR: E:/Vivado/2018.3/bin

Vivado preferences file location: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/vivado.xml
Vivado preferences directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/
Vivado layouts directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/layouts
PlanAhead jar file location: 	E:/Vivado/2018.3/lib/classes/planAhead.jar
Vivado log file location: 	H:/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.log
Vivado journal file location: 	H:/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.jou
Engine tmp dir: 	H:/lab2/proj_miniRV_ego1/proj_single_cycle/.Xil/Vivado-30240-L

Xilinx Environment Variables
----------------------------
XILINX: E:/Vivado/2018.3/ids_lite/ISE
XILINX_DSP: E:/Vivado/2018.3/ids_lite/ISE
XILINX_PLANAHEAD: E:/Vivado/2018.3
XILINX_SDK: E:/SDK/2018.3
XILINX_VIVADO: E:/Vivado/2018.3
XILINX_VIVADO_HLS: E:/Vivado/2018.3


GUI allocated memory:	128 MB
GUI max memory:		3,072 MB
Engine allocated memory: 675 MB

Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

*/

// TclEventType: START_GUI
// Tcl Message: start_gui 
// TclEventType: PROJECT_OPEN_DIALOG
// [GUI Memory]: 86 MB (+88017kb) [00:00:09]
// [Engine Memory]: 614 MB (+492435kb) [00:00:09]
// bx (cp):  Open Project : addNotify
// Opening Vivado Project: H:\lab2\proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr. Version: Vivado v2018.3 
// TclEventType: DEBUG_PROBE_SET_CHANGE
// Tcl Message: open_project H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr 
// TclEventType: MSGMGR_MOVEMSG
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: FILE_SET_NEW
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_CURRENT
// TclEventType: PROJECT_DASHBOARD_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: PROJECT_NEW
// [GUI Memory]: 120 MB (+31278kb) [00:00:13]
// [Engine Memory]: 738 MB (+98125kb) [00:00:13]
// WARNING: HEventQueue.dispatchEvent() is taking  2419 ms.
// Tcl Message: open_project H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr 
// Tcl Message: INFO: [Project 1-313] Project file moved from 'H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle' since last save. 
// Tcl Message: Scanning sources... Finished scanning sources 
// Tcl Message: INFO: [IP_Flow 19-234] Refreshing IP repositories INFO: [IP_Flow 19-1704] No user IP repositories specified INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'. 
// Tcl Message: ERROR: [IP_Flow 19-993] Could not find IP file for IP 'cpuclk'. 
// HMemoryUtils.trashcanNow. Engine heap size: 787 MB. GUI used memory: 63 MB. Current time: 7/21/25, 9:03:20 AM CST
// [Engine Memory]: 787 MB (+12317kb) [00:00:16]
// Tcl Message: open_project: Time (s): cpu = 00:00:12 ; elapsed = 00:00:06 . Memory (MB): peak = 839.598 ; gain = 166.688 
// Tcl Message: ERROR: [Common 17-39] 'open_project' failed due to earlier errors. 
// CommandFailedException: ERROR: [Common 17-69] Command failed: ERROR: [Common 17-39] 'open_project' failed due to earlier errors.  
// [Engine Memory]: 849 MB (+23260kb) [00:00:17]
dismissDialog("Open Project"); // bx (cp)
