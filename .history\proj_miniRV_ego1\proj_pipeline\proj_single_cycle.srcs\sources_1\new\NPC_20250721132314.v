`timescale 1ns / 1ps
`include "defines.vh"

module NPC (
    // 输入信号
    input  wire [31:0] PC,      
    input  wire [31:0] offset,  
    input  wire        br,      
    input  wire [1:0]  op,      
    input  wire        stop,    
    input  wire [31:0] ex_pc,   

    // 输出信号
    output reg         branch,  
    output reg  [31:0] npc,    
    output wire [31:0] pc4    
);

    assign pc4 = PC + 3'd4;

    // 下一PC计算
    always @(*) begin
        if (~stop) begin
            case (op)
                `NPC_PC4: begin
                    npc = PC + 3'd4;  // 顺序执行
                end
                `NPC_BEQ: begin
                    npc = br ? ex_pc + offset : PC + 3'd4;  // 条件分支
                end
                `NPC_JMP: begin
                    npc = ex_pc + offset;  // 无条件跳转
                end
                default: begin
                    npc = PC + 3'd4;
                end
            endcase
        end else begin
            npc = PC;  // 停止时保持当前PC
        end
    end

    // 分支标志计算
    always @(*) begin
        if (~stop) begin
            case (op)
                `NPC_BEQ: begin
                    branch = br ? 1'b1 : 1'b0;  // 分支指令
                end
                `NPC_JMP: begin
                    branch = 1'b1;  // 跳转指令
                end
                default: begin
                    branch = 1'b0;  // 其他指令
                end
            endcase
        end else begin
            branch = 1'b0;  // 停止时无分支
        end
    end
endmodule
