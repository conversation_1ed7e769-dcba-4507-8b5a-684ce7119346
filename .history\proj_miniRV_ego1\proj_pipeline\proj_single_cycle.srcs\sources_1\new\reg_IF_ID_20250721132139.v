`timescale 1ns / 1ps

// IF/ID流水线寄存器
module reg_IF_ID (
    // 控制信号
    input  wire        clk,       // 时钟信号
    input  wire        rst,       // 复位信号
    input  wire        clear,     // 清除信号
    input  wire        stop,      // 停止信号

    // IF阶段输入
    input  wire [31:0] if_pc,     // IF阶段PC
    input  wire [31:0] if_pc4,    // IF阶段PC+4
    input  wire [31:0] if_inst,   // IF阶段指令

    // ID阶段输出
    output reg  [31:0] id_pc,     // ID阶段PC
    output reg  [31:0] id_pc4,    // ID阶段PC+4
    output reg  [31:0] id_inst    // ID阶段指令
);

    // 流水线寄存器更新逻辑
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            id_pc   <= 32'd0;
            id_pc4  <= 32'd0;
            id_inst <= 32'd0;
        end else if (clear) begin  // 清除（插入气泡）
            id_pc   <= 32'd0;
            id_pc4  <= 32'd0;
            id_inst <= 32'd0;
        end else if (stop) begin   // 停止（保持当前值）
            id_pc   <= id_pc;
            id_pc4  <= id_pc4;
            id_inst <= id_inst;
        end else begin             // 正常传递
            id_pc   <= if_pc;
            id_pc4  <= if_pc4;
            id_inst <= if_inst;
        end
    end
endmodule
