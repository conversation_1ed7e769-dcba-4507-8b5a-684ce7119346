`timescale 1ns / 1ps

module RF (
    // 读端口
    input  wire [4:0]  rR1,     
    input  wire [4:0]  rR2,     
    output reg  [31:0] rD1,     
    output reg  [31:0] rD2,     

    // 写端口
    input  wire [4:0]  wR,       // 写寄存器地址
    input  wire        we,       // 写使能
    input  wire [31:0] wD,       // 写数据

    input  wire        clk     
);
    // 寄存器堆，31个32位寄存器（不包括x0）
    reg [31:0] regts[1:31];

    // 读寄存器1
    always @(*) begin
        rD1 = (rR1 == 5'h0) ? 32'd0 : regts[rR1];
    end

    // 读寄存器2
    always @(*) begin
        rD2 = (rR2 == 5'h0) ? 32'd0 : regts[rR2]; 
    end

    // 写寄存器逻辑
    always @(posedge clk) begin
        if (we & (wR != 5'h0)) begin 
            regts[wR] <= wD;
        end
    end
endmodule
