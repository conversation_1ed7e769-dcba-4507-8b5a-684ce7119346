<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Wed Jul 16 16:07:55 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="af376fe724ed414d9dd6f0a900d6efa1" type="ProjectID"/>
<property name="ProjectIteration" value="37" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Vivado Synthesis Defaults" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="AddSources" value="21" type="JavaHandler"/>
<property name="AutoConnectTarget" value="8" type="JavaHandler"/>
<property name="CoreView" value="9" type="JavaHandler"/>
<property name="CustomizeCore" value="6" type="JavaHandler"/>
<property name="EditDelete" value="7" type="JavaHandler"/>
<property name="LaunchProgramFpga" value="21" type="JavaHandler"/>
<property name="OpenHardwareManager" value="31" type="JavaHandler"/>
<property name="OpenRecentTarget" value="9" type="JavaHandler"/>
<property name="ProgramDevice" value="18" type="JavaHandler"/>
<property name="RecustomizeCore" value="9" type="JavaHandler"/>
<property name="RunBitgen" value="41" type="JavaHandler"/>
<property name="RunImplementation" value="1" type="JavaHandler"/>
<property name="RunSynthesis" value="1" type="JavaHandler"/>
<property name="ShowView" value="7" type="JavaHandler"/>
<property name="ToolsSettings" value="10" type="JavaHandler"/>
<property name="ViewTaskProjectManager" value="14" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="AddSrcWizard_SPECIFY_HDL_NETLIST_BLOCK_DESIGN" value="1" type="GuiHandlerData"/>
<property name="AddSrcWizard_SPECIFY_SIMULATION_SPECIFIC_HDL_FILES" value="1" type="GuiHandlerData"/>
<property name="BaseDialog_APPLY" value="1" type="GuiHandlerData"/>
<property name="BaseDialog_CANCEL" value="7" type="GuiHandlerData"/>
<property name="BaseDialog_OK" value="133" type="GuiHandlerData"/>
<property name="BaseDialog_YES" value="13" type="GuiHandlerData"/>
<property name="CmdMsgDialog_OK" value="5" type="GuiHandlerData"/>
<property name="CoreTreeTablePanel_CORE_TREE_TABLE" value="27" type="GuiHandlerData"/>
<property name="CreateSrcFileDialog_FILE_NAME" value="34" type="GuiHandlerData"/>
<property name="CustomizeErrorDialog_MESSAGES" value="4" type="GuiHandlerData"/>
<property name="CustomizeErrorDialog_OK" value="2" type="GuiHandlerData"/>
<property name="DefineModulesDialog_NEW_SOURCE_FILES" value="5" type="GuiHandlerData"/>
<property name="ExpRunTreePanel_EXP_RUN_TREE_TABLE" value="1" type="GuiHandlerData"/>
<property name="FileSetPanel_FILE_SET_PANEL_TREE" value="640" type="GuiHandlerData"/>
<property name="FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE" value="134" type="GuiHandlerData"/>
<property name="HJFileChooserHelpers_JUMP_TO_RECENT_PROJECT_DIRECTORY" value="5" type="GuiHandlerData"/>
<property name="HPopupTitle_CLOSE" value="1" type="GuiHandlerData"/>
<property name="HardwareTreePanel_HARDWARE_TREE_TABLE" value="3" type="GuiHandlerData"/>
<property name="IPCoreView_TABBED_PANE" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_TOOLS" value="14" type="GuiHandlerData"/>
<property name="MessageWithOptionDialog_DONT_SHOW_THIS_DIALOG_AGAIN" value="4" type="GuiHandlerData"/>
<property name="MsgTreePanel_MESSAGE_SEVERITY" value="12" type="GuiHandlerData"/>
<property name="MsgTreePanel_MESSAGE_VIEW_TREE" value="27" type="GuiHandlerData"/>
<property name="MsgView_CLEAR_MESSAGES_RESULTING_FROM_USER_EXECUTED" value="2" type="GuiHandlerData"/>
<property name="NumJobsChooser_NUMBER_OF_JOBS" value="6" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_CONNECT_TARGET" value="8" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_UPDATE_HIER" value="5" type="GuiHandlerData"/>
<property name="PACommandNames_PROGRAM_FPGA" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_RUN_BITGEN" value="1" type="GuiHandlerData"/>
<property name="PAViews_CODE" value="5" type="GuiHandlerData"/>
<property name="PAViews_IP_CATALOG" value="1" type="GuiHandlerData"/>
<property name="PAViews_PROJECT_SUMMARY" value="21" type="GuiHandlerData"/>
<property name="ProgramFpgaDialog_PROGRAM" value="22" type="GuiHandlerData"/>
<property name="ProgramFpgaDialog_SPECIFY_BITSTREAM_FILE" value="1" type="GuiHandlerData"/>
<property name="ProgressDialog_CANCEL" value="1" type="GuiHandlerData"/>
<property name="ProjectDashboardView_DASHBOARD" value="1" type="GuiHandlerData"/>
<property name="RDICommands_CUSTOM_COMMANDS" value="6" type="GuiHandlerData"/>
<property name="RDICommands_DELETE" value="5" type="GuiHandlerData"/>
<property name="RDICommands_SETTINGS" value="7" type="GuiHandlerData"/>
<property name="SettingsDialog_OPTIONS_TREE" value="6" type="GuiHandlerData"/>
<property name="SettingsEditorPage_ENTER_COMMAND_LINE_FOR_CUSTOM" value="6" type="GuiHandlerData"/>
<property name="SettingsEditorPage_USE_THIS_DROP_DOWN_LIST_BOX_TO_SELECT" value="6" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_GENERATE_OUTPUT_PRODUCTS_IMMEDIATELY" value="11" type="GuiHandlerData"/>
<property name="SrcChooserPanel_CREATE_FILE" value="33" type="GuiHandlerData"/>
<property name="SrcMenu_IP_HIERARCHY" value="4" type="GuiHandlerData"/>
<property name="SrcMenu_REFRESH_HIERARCHY" value="1" type="GuiHandlerData"/>
<property name="TclConsoleView_CLEAR_ALL_OUTPUT_IN_TCL_CONSOLE" value="3" type="GuiHandlerData"/>
<property name="TclConsoleView_TCL_CONSOLE_CODE_EDITOR" value="1" type="GuiHandlerData"/>
<property name="TouchpointSurveyDialog_NO" value="1" type="GuiHandlerData"/>
<property name="XPG_CoeFileWidgdet_BROWSE" value="5" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="11" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="10" type="TclMode"/>
</item>
</section>
</application>
</document>
