#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Fri Jul 11 13:33:03 2025
# Process ID: 26972
# Current directory: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent22016 H:\lab2\onboard_proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr
# Log file: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/vivado.log
# Journal file: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr
update_compile_order -fileset sources_1
reset_run synth_1
reset_run IROM_synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
launch_runs impl_1 -to_step write_bitstream -jobs 32
set_property -dict [list CONFIG.coefficient_file {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/test.coe}] [get_ips IROM]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
catch { config_ip_cache -export [get_ips -all IROM] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -sync -force -quiet
launch_runs -jobs 32 IROM_synth_1
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
export_ip_user_files -of_objects  [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci] -no_script -reset -force -quiet
remove_files  -fileset DRAM C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci
export_ip_user_files -of_objects  [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -reset -force -quiet
remove_files  -fileset IROM C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci
create_ip -name dist_mem_gen -vendor xilinx.com -library ip -version 8.0 -module_name IROM -dir h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.depth {16384} CONFIG.data_width {32} CONFIG.Component_Name {IROM} CONFIG.memory_type {rom}] [get_ips IROM]
generate_target {instantiation_template} [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
generate_target all [get_files  h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
catch { config_ip_cache -export [get_ips -all IROM] }
export_ip_user_files -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
export_simulation -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
set_property -dict [list CONFIG.coefficient_file {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/test.coe}] [get_ips IROM]
generate_target all [get_files  h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
catch { config_ip_cache -export [get_ips -all IROM] }
export_ip_user_files -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
launch_runs -jobs 32 IROM_synth_1
export_simulation -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
create_ip -name dist_mem_gen -vendor xilinx.com -library ip -version 8.0 -module_name DRAM -dir h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.depth {16384} CONFIG.data_width {32} CONFIG.Component_Name {DRAM}] [get_ips DRAM]
generate_target {instantiation_template} [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci]
generate_target all [get_files  h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci]
catch { config_ip_cache -export [get_ips -all DRAM] }
export_ip_user_files -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci]
export_simulation -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
update_compile_order -fileset sources_1
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
open_hw
connect_hw_server
open_hw_target
set_property PROGRAM.FILE {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
current_hw_device [get_hw_devices xc7a35t_0]
refresh_hw_device -update_hw_probes false [lindex [get_hw_devices xc7a35t_0] 0]
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
