#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Mon Jul 21 12:38:06 2025
# Process ID: 13240
# Current directory: H:/lab2/proj_miniRV_ego1/proj_pipeline
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent37500 H:\lab2\proj_miniRV_ego1\proj_pipeline\proj_single_cycle.xpr
# Log file: H:/lab2/proj_miniRV_ego1/proj_pipeline/vivado.log
# Journal file: H:/lab2/proj_miniRV_ego1/proj_pipeline\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.xpr
update_compile_order -fileset sources_1
create_ip -name clk_wiz -vendor xilinx.com -library ip -version 6.0 -module_name clk_wiz_0 -dir h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.PRIMITIVE {PLL} CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.CLKOUT1_DRIVES {BUFG} CONFIG.CLKOUT2_DRIVES {BUFG} CONFIG.CLKOUT3_DRIVES {BUFG} CONFIG.CLKOUT4_DRIVES {BUFG} CONFIG.CLKOUT5_DRIVES {BUFG} CONFIG.CLKOUT6_DRIVES {BUFG} CONFIG.CLKOUT7_DRIVES {BUFG} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_COMPENSATION {ZHOLD} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips clk_wiz_0]
generate_target {instantiation_template} [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xci]
update_compile_order -fileset sources_1
export_ip_user_files -of_objects  [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xci] -no_script -reset -force -quiet
remove_files  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xci
export_ip_user_files -of_objects  [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -reset -force -quiet
remove_files  -fileset cpuclk C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci
create_ip -name clk_wiz -vendor xilinx.com -library ip -version 6.0 -module_name clk_wiz_0 -dir h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.PRIMITIVE {PLL} CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.CLKOUT1_DRIVES {BUFG} CONFIG.CLKOUT2_DRIVES {BUFG} CONFIG.CLKOUT3_DRIVES {BUFG} CONFIG.CLKOUT4_DRIVES {BUFG} CONFIG.CLKOUT5_DRIVES {BUFG} CONFIG.CLKOUT6_DRIVES {BUFG} CONFIG.CLKOUT7_DRIVES {BUFG} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_COMPENSATION {ZHOLD} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips clk_wiz_0]
generate_target {instantiation_template} [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0_1/clk_wiz_0.xci]
update_compile_order -fileset sources_1
export_ip_user_files -of_objects  [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0_1/clk_wiz_0.xci] -no_script -reset -force -quiet
remove_files  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/clk_wiz_0_1/clk_wiz_0.xci
create_ip -name clk_wiz -vendor xilinx.com -library ip -version 6.0 -module_name cpuclk -dir h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.Component_Name {cpuclk} CONFIG.PRIMITIVE {PLL} CONFIG.CLKOUT1_REQUESTED_OUT_FREQ {90} CONFIG.CLKOUT1_DRIVES {BUFG} CONFIG.CLKOUT2_DRIVES {BUFG} CONFIG.CLKOUT3_DRIVES {BUFG} CONFIG.CLKOUT4_DRIVES {BUFG} CONFIG.CLKOUT5_DRIVES {BUFG} CONFIG.CLKOUT6_DRIVES {BUFG} CONFIG.CLKOUT7_DRIVES {BUFG} CONFIG.MMCM_DIVCLK_DIVIDE {1} CONFIG.MMCM_CLKFBOUT_MULT_F {9} CONFIG.MMCM_COMPENSATION {ZHOLD} CONFIG.MMCM_CLKOUT0_DIVIDE_F {10} CONFIG.CLKOUT1_JITTER {140.709} CONFIG.CLKOUT1_PHASE_ERROR {105.461}] [get_ips cpuclk]
generate_target {instantiation_template} [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
generate_target all [get_files  h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
catch { config_ip_cache -export [get_ips -all cpuclk] }
export_ip_user_files -of_objects [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
launch_runs -jobs 32 cpuclk_synth_1
export_simulation -of_objects [get_files h:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci] -directory H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/proj_miniRV_ego1/proj_pipeline/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
wait_on_run impl_1
