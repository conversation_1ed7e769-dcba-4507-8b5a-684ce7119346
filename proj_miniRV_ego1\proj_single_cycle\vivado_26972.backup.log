#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Fri Jul 11 13:33:03 2025
# Process ID: 26972
# Current directory: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent22016 H:\lab2\onboard_proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr
# Log file: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/vivado.log
# Journal file: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr
INFO: [Project 1-313] Project file moved from 'H:/lab2/成功上板的lab2/lab2/proj_miniRV_ego1/proj_single_cycle' since last save.
CRITICAL WARNING: [Project 1-19] Could not find the file 'c:/Users/<USER>/Desktop/实验/cpu设计/lab2/proj_miniRV_ego1/proj_single_cycle/test.coe'.
Scanning sources...
Finished scanning sources
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
open_project: Time (s): cpu = 00:00:13 ; elapsed = 00:00:06 . Memory (MB): peak = 872.086 ; gain = 215.891
update_compile_order -fileset sources_1
reset_run synth_1
reset_run IROM_synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'IROM'...
ERROR: [Ipptcl 7-519] NULL COE Data pointer: Unable to open the file 
ERROR: [Common 17-39] 'ipgui::get_coe_val' failed due to earlier errors.
CRITICAL WARNING: [IP_Flow 19-1747] Failed to deliver file 'e:/Vivado/2018.3/data/ip/xilinx/dist_mem_gen_v8_0/hdl/mem_init_file.xit': ERROR: [Common 17-39] 'ipgui::get_coe_val' failed due to earlier errors.

ERROR: [IP_Flow 19-167] Failed to deliver one or more file(s).
ERROR: [IP_Flow 19-3505] IP Generation error: Failed to generate IP 'IROM'. Failed to generate 'Vivado VHDL Synthesis' outputs: 
ERROR: [IP_Flow 19-98] Generation of the IP CORE failed.
Failed to generate IP 'IROM'. Failed to generate 'Vivado VHDL Synthesis' outputs: 
launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'IROM'...
ERROR: [Ipptcl 7-519] NULL COE Data pointer: Unable to open the file 
ERROR: [Common 17-39] 'ipgui::get_coe_val' failed due to earlier errors.
CRITICAL WARNING: [IP_Flow 19-1747] Failed to deliver file 'e:/Vivado/2018.3/data/ip/xilinx/dist_mem_gen_v8_0/hdl/mem_init_file.xit': ERROR: [Common 17-39] 'ipgui::get_coe_val' failed due to earlier errors.

ERROR: [IP_Flow 19-167] Failed to deliver one or more file(s).
ERROR: [IP_Flow 19-3505] IP Generation error: Failed to generate IP 'IROM'. Failed to generate 'Vivado VHDL Synthesis' outputs: 
ERROR: [IP_Flow 19-98] Generation of the IP CORE failed.
Failed to generate IP 'IROM'. Failed to generate 'Vivado VHDL Synthesis' outputs: 
set_property -dict [list CONFIG.coefficient_file {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/test.coe}] [get_ips IROM]
generate_target all [get_files  C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'IROM'...
catch { config_ip_cache -export [get_ips -all IROM] }
export_ip_user_files -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -sync -force -quiet
launch_runs -jobs 32 IROM_synth_1
[Fri Jul 11 13:58:43 2025] Launched IROM_synth_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/runme.log
export_simulation -of_objects [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
launch_runs impl_1 -to_step write_bitstream -jobs 32
[Fri Jul 11 13:58:51 2025] Launched IROM_synth_1, synth_1...
Run output will be captured here:
IROM_synth_1: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/runme.log
synth_1: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/runme.log
[Fri Jul 11 13:58:51 2025] Launched impl_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/runme.log
export_ip_user_files -of_objects  [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci] -no_script -reset -force -quiet
remove_files  -fileset DRAM C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci
INFO: [Project 1-386] Moving file 'C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' from fileset 'DRAM' to fileset 'sources_1'.
export_ip_user_files -of_objects  [get_files C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -reset -force -quiet
remove_files  -fileset IROM C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci
INFO: [Project 1-386] Moving file 'C:/Users/<USER>/Desktop/cpu/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci' from fileset 'IROM' to fileset 'sources_1'.
create_ip -name dist_mem_gen -vendor xilinx.com -library ip -version 8.0 -module_name IROM -dir h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.depth {16384} CONFIG.data_width {32} CONFIG.Component_Name {IROM} CONFIG.memory_type {rom}] [get_ips IROM]
WARNING: [Vivado 12-3523] Attempt to change 'Component_Name' from 'IROM' to 'IROM' is not allowed and is ignored.
generate_target {instantiation_template} [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'IROM'...
generate_target all [get_files  h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'IROM'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'IROM'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'IROM'...
catch { config_ip_cache -export [get_ips -all IROM] }
INFO: [IP_Flow 19-4993] Using cached IP synthesis design for IP IROM, cache-ID = ec029074e01c657e; cache size = 4.197 MB.
export_ip_user_files -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
INFO: [Vivado 12-3453] The given sub-design is up-to-date, no action was taken.  If a run is still desired, use the '-force' option for the file:'h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci'
export_simulation -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
set_property -dict [list CONFIG.coefficient_file {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/test.coe}] [get_ips IROM]
INFO: [IP_Flow 19-3484] Absolute path of file 'h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/test.coe' provided. It will be converted relative to IP Instance files '../../../../test.coe'
generate_target all [get_files  h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'IROM'...
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'IROM'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'IROM'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'IROM'...
catch { config_ip_cache -export [get_ips -all IROM] }
export_ip_user_files -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci]
launch_runs -jobs 32 IROM_synth_1
[Fri Jul 11 14:06:00 2025] Launched IROM_synth_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/runme.log
export_simulation -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/IROM/IROM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
create_ip -name dist_mem_gen -vendor xilinx.com -library ip -version 8.0 -module_name DRAM -dir h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.depth {16384} CONFIG.data_width {32} CONFIG.Component_Name {DRAM}] [get_ips DRAM]
WARNING: [Vivado 12-3523] Attempt to change 'Component_Name' from 'DRAM' to 'DRAM' is not allowed and is ignored.
generate_target {instantiation_template} [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci]
INFO: [IP_Flow 19-1686] Generating 'Instantiation Template' target for IP 'DRAM'...
generate_target all [get_files  h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci]
INFO: [IP_Flow 19-1686] Generating 'Synthesis' target for IP 'DRAM'...
INFO: [IP_Flow 19-1686] Generating 'Simulation' target for IP 'DRAM'...
INFO: [IP_Flow 19-1686] Generating 'Change Log' target for IP 'DRAM'...
catch { config_ip_cache -export [get_ips -all DRAM] }
INFO: [IP_Flow 19-4993] Using cached IP synthesis design for IP DRAM, cache-ID = 63d0c76c10e9b1cd; cache size = 4.197 MB.
export_ip_user_files -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci] -no_script -sync -force -quiet
create_ip_run [get_files -of_objects [get_fileset sources_1] h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci]
INFO: [Vivado 12-3453] The given sub-design is up-to-date, no action was taken.  If a run is still desired, use the '-force' option for the file:'h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci'
export_simulation -of_objects [get_files h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci] -directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/sim_scripts -ip_user_files_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files -ipstatic_source_dir H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.ip_user_files/ipstatic -lib_map_path [list {modelsim=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/modelsim} {questa=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/questa} {riviera=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/riviera} {activehdl=H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.cache/compile_simlib/activehdl}] -use_ip_compiled_libs -force -quiet
update_compile_order -fileset sources_1
reset_run synth_1
WARNING: [Vivado 12-1017] Problems encountered:
1. Failed to delete one or more files in run directory H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1

launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Fri Jul 11 14:08:23 2025] Launched IROM_synth_1, synth_1...
Run output will be captured here:
IROM_synth_1: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/IROM_synth_1/runme.log
synth_1: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/runme.log
[Fri Jul 11 14:08:23 2025] Launched impl_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/runme.log
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Fri Jul 11 14:12:03 2025] Launched synth_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/runme.log
[Fri Jul 11 14:12:03 2025] Launched impl_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/runme.log
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
INFO: [Labtools 27-2222] Launching hw_server...
INFO: [Labtools 27-2221] Launch Output:

****** Xilinx hw_server v2018.3
  **** Build date : Dec  7 2018-00:40:27
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.


open_hw_target
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Xilinx/1234-tulA
set_property PROGRAM.FILE {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
current_hw_device [get_hw_devices xc7a35t_0]
refresh_hw_device -update_hw_probes false [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
INFO: [Vivado 12-4149] The synthesis checkpoint for IP 'h:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/DRAM/DRAM.xci' is already up-to-date
[Fri Jul 11 14:43:28 2025] Launched synth_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/runme.log
[Fri Jul 11 14:43:28 2025] Launched impl_1...
Run output will be captured here: H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/runme.log
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/onboard_proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
ERROR: [Labtoolstcl 44-513] HW Target shutdown. Closing target: localhost:3121/xilinx_tcf/Xilinx/1234-tulA
exit
INFO: [Common 17-206] Exiting Vivado at Fri Jul 11 15:15:36 2025...
