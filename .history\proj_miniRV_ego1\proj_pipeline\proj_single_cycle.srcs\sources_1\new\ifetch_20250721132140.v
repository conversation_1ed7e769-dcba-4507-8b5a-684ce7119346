`timescale 1ns / 1ps

// 取指模块
module ifetch (
    // 控制信号
    input  wire        reset,      // 复位信号
    input  wire        clk,        // 时钟信号
    input  wire        pc_sel,     // PC选择信号
    input  wire        br,         // 分支条件
    input  wire [1:0]  npc_op,     // NPC操作码
    input  wire        stop,       // 停止信号

    // 数据输入
    input  wire [31:0] alu,        // ALU计算结果（用于JALR指令）
    input  wire [31:0] offset,     // 偏移量
    input  wire [31:0] ex_pc,      // 执行阶段PC

    // 输出信号
    output wire [31:0] pc4,        // PC+4
    output wire [31:0] pc,         // 当前PC
    output wire        branch      // 分支标志
);

    // 内部连线
    wire [31:0] npc;               // 下一PC值
    wire [31:0] npc_din;           // PC模块输入

    // PC选择逻辑
    assign npc_din = pc_sel ? alu : npc;  // JALR指令选择ALU结果，否则选择NPC模块输出

    // NPC模块实例化
    NPC npc_module (
        .op(npc_op),
        .br(br),
        .offset(offset),
        .PC(pc),
        .npc(npc),
        .pc4(pc4),
        .stop(stop),
        .ex_pc(ex_pc),
        .branch(branch)
    );

    // PC模块实例化
    PC pc_module (
        .clk(clk),
        .rst(reset),
        .pc(pc),
        .npc(npc_din)
    );
endmodule

