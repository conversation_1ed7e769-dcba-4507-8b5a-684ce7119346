`timescale 1ns / 1ps
`include "defines.vh"

module control (
    // 指令解码输入
    input  wire [6:0] opcode,      
    input  wire [2:0] funct3,      
    input  wire [6:0] funct7,      

    // 存储器控制信号
    output reg  [1:0] ram_wdin_op, 
    output reg  [2:0] ram_rb_op,   
    output reg        ram_we,     

    // PC控制信号
    output reg        pc_sel,     
    output reg  [1:0] npc_op,     

    // ALU控制信号
    output reg        alub_sel,     
    output reg        alua_sel,    
    output reg  [3:0] alu_op,     

    // 寄存器文件控制信号
    output reg  [2:0] sext_op,     
    output reg  [1:0] rf_wsel,     
    output reg        rf_we     
);
    // NPC操作控制
    always @(*) begin
        case (opcode)
            7'b1100011: npc_op = `NPC_BEQ;  // 分支指令
            7'b1101111: npc_op = `NPC_JMP;  // 跳转指令
            default:    npc_op = `NPC_PC4;  // 默认PC+4
        endcase
    end
    // 寄存器写使能控制
    always @(*) begin
        case (opcode)
            7'b0100011: rf_we = 1'b0;  // SW指令，不写寄存器
            7'b1100011: rf_we = 1'b0;  // 分支指令，不写寄存器
            7'b0110011: rf_we = 1'b1;  // R型指令
            7'b0010011: rf_we = 1'b1;  // I型指令
            7'b0000011: rf_we = 1'b1;  // LW指令
            7'b1100111: rf_we = 1'b1;  // JALR指令
            7'b0110111: rf_we = 1'b1;  // LUI指令
            7'b1101111: rf_we = 1'b1;  // JAL指令
            default:    rf_we = 1'b0;
        endcase
    end
    // 寄存器写数据选择控制
    always @(*) begin
        case (opcode)
            7'b0110011: rf_wsel = `WD_ALUC;  // R型指令，写ALU结果
            7'b0010011: rf_wsel = `WD_ALUC;  // I型指令，写ALU结果
            7'b0000011: rf_wsel = `WD_RAM;   // LW指令，写存储器数据
            7'b1100111: rf_wsel = `WD_PC4;   // JALR指令，写PC+4
            7'b0110111: rf_wsel = `WD_EXT;   // LUI指令，写扩展立即数
            7'b1101111: rf_wsel = `WD_PC4;   // JAL指令，写PC+4
            default:    rf_wsel = `WD_PC4;
        endcase
    end
    // 符号扩展操作控制
    always @(*) begin
        case (opcode)
            7'b0100011: sext_op = `SEXT_S;  
            7'b1100011: sext_op = `SEXT_B; 
            7'b0110111: sext_op = `SEXT_U; 
            7'b1101111: sext_op = `SEXT_J;  
            default:    sext_op = `SEXT_I; 
        endcase
    end


    // ALU操作控制
    always @(*) begin
        case (opcode)
            7'b0110011: begin  // R型指令
                case (funct3)
                    3'b000: alu_op = funct7[5] ? `ALU_SUB : `ALU_ADD;  // ADD/SUB
                    3'b111: alu_op = `ALU_AND;                         // AND
                    3'b110: alu_op = `ALU_OR;                          // OR
                    3'b100: alu_op = `ALU_XOR;                         // XOR
                    3'b001: alu_op = `ALU_SLL;                         // SLL
                    3'b101: alu_op = funct7[5] ? `ALU_SRA : `ALU_SRL;  // SRL/SRA
                    default: alu_op = `ALU_ADD;
                endcase
            end
            7'b0010011: begin  // I型指令
                case (funct3)
                    3'b000: alu_op = `ALU_ADD;                         // ADDI
                    3'b111: alu_op = `ALU_AND;                         // ANDI
                    3'b110: alu_op = `ALU_OR;                          // ORI
                    3'b100: alu_op = `ALU_XOR;                         // XORI
                    3'b001: alu_op = `ALU_SLL;                         // SLLI
                    3'b101: alu_op = funct7[5] ? `ALU_SRA : `ALU_SRL;  // SRLI/SRAI
                    default: alu_op = `ALU_ADD;
                endcase
            end
            7'b1100011: begin  // 分支指令
                case (funct3)
                    3'b000: alu_op = `ALU_EQ;   // BEQ
                    3'b001: alu_op = `ALU_NE;   // BNE
                    3'b100: alu_op = `ALU_LT;   // BLT
                    3'b101: alu_op = `ALU_GE;   // BGE
                    default: alu_op = `ALU_ADD;
                endcase
            end
            default: alu_op = `ALU_ADD; 
        endcase
    end
    // ALU A端
    always @(*) begin
        alua_sel = 1'b1;  // 选择寄存器值
    end

    // ALU B端选择控制
    always @(*) begin
        case (opcode)
            7'b0110011: alub_sel = 1'b1;  // R型指令，选择寄存器值
            7'b1100011: alub_sel = 1'b1;  // 分支指令，选择寄存器值
            default:    alub_sel = 1'b0;  // 其他指令，选择立即数
        endcase
    end

    // PC选择控制
    always @(*) begin
        pc_sel = (opcode == 7'b1100111) ? 1'b1 : 1'b0;  // JALR指令选择寄存器值
    end

    // 存储器写使能控制
    always @(*) begin
        ram_we = (opcode == 7'b0100011) ? 1'b1 : 1'b0;  // SW指令使能写存储器
    end

    // 存储器写数据操作控制
    always @(*) begin
        ram_wdin_op = `WRAM_SW;  // 只支持SW指令
    end

    // 存储器读数据操作控制
    always @(*) begin
        ram_rb_op = `RDO_LW;  // 只支持LW指令
    end
endmodule
