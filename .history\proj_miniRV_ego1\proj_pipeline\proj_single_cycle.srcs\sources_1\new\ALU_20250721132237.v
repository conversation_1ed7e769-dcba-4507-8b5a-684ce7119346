`timescale 1ns / 1ps
`include "defines.vh"

module ALU (
    // 控制信号
    input  wire [3:0]  op,   
    // 数据信号
    input  wire [31:0] A,    
    input  wire [31:0] B,    

    output wire [31:0] C,    
    output wire        f    
);

    reg [31:0] resultC;
    reg        resultf;

    assign C = resultC;
    assign f = resultf;
    
    wire [4:0] shamt = B[4:0];

    always @(*) begin
        case (op)
            `ALU_ADD: begin
                resultC = A + B;
                resultf = 1'b0;
            end
            `ALU_SUB: begin
                resultC = A - B;
                resultf = 1'b0;
            end
            `ALU_AND: begin
                resultC = A & B;
                resultf = 1'b0;
            end
            `ALU_OR: begin
                resultC = A | B;
                resultf = 1'b0;
            end
            `ALU_XOR: begin
                resultC = A ^ B;
                resultf = 1'b0;
            end
            `ALU_SLL: begin
                resultC = A << shamt;
                resultf = 1'b0;
            end
            `ALU_SRL: begin
                resultC = A >> shamt;
                resultf = 1'b0;
            end
            `ALU_SRA: begin
                resultC = $signed(A) >>> shamt;  
                resultf = 1'b0;
            end
            `ALU_EQ: begin 
                resultC = 32'h0;
                resultf = (A == B);
            end
            `ALU_NE: begin  
                resultC = 32'h0;
                resultf = (A != B);
            end
            `ALU_LT: begin 
                resultC = ($signed(A) < $signed(B));
                resultf = ($signed(A) < $signed(B));
            end
            `ALU_GE: begin  
                resultC = ($signed(A) >= $signed(B));
                resultf = ($signed(A) >= $signed(B));
            end
            default: begin
                resultC = 32'h0;
                resultf = 1'b0;
            end
        endcase
    end
endmodule
