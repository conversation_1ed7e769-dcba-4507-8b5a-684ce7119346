<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>clk_wiz_0</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:busInterfaces>
    <spirit:busInterface>
      <spirit:name>s_axi_lite</spirit:name>
      <spirit:displayName>S_AXI_LITE</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_araddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awaddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WSTRB</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wstrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>DATA_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.DATA_WIDTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PROTOCOL</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.PROTOCOL">AXI4LITE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ID_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.ID_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ADDR_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.ADDR_WIDTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>AWUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.AWUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ARUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.ARUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>WUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.WUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>RUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.RUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>BUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.BUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>READ_WRITE_MODE</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.READ_WRITE_MODE">READ_WRITE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_BURST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_LOCK</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_LOCK">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_PROT</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_PROT">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_CACHE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_CACHE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_QOS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_QOS">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_REGION</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_REGION">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_WSTRB</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_WSTRB">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_BRESP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_BRESP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_RRESP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_RRESP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>SUPPORTS_NARROW_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.SUPPORTS_NARROW_BURST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_READ_OUTSTANDING</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.NUM_READ_OUTSTANDING">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_WRITE_OUTSTANDING</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.NUM_WRITE_OUTSTANDING">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MAX_BURST_LENGTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.MAX_BURST_LENGTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.PHASE">0.000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_READ_THREADS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.NUM_READ_THREADS">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_WRITE_THREADS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.NUM_WRITE_THREADS">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>RUSER_BITS_PER_BYTE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.RUSER_BITS_PER_BYTE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>WUSER_BITS_PER_BYTE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.WUSER_BITS_PER_BYTE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.S_AXI_LITE.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.s_axi_lite" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>s_axi_aclk</spirit:name>
      <spirit:displayName>s_axi_aclk</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S_AXI_ACLK.ASSOCIATED_BUSIF">s_axi_lite</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S_AXI_ACLK.ASSOCIATED_RESET">s_axi_aresetn</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_ACLK.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_ACLK.PHASE">0.000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXI_ACLK.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.S_AXI_ACLK.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.s_axi_aclk" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>ref_clk</spirit:name>
      <spirit:displayName>ref_clk</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>ref_clk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.REF_CLK.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.REF_CLK.PHASE">0.000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.REF_CLK.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.REF_CLK.ASSOCIATED_BUSIF"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.REF_CLK.ASSOCIATED_RESET"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.REF_CLK.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.ref_clk" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>s_axi_resetn</spirit:name>
      <spirit:displayName>S_AXI_RESETN</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S_AXI_RESETN.ASSOCIATED_RESET">aresetn</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S_AXI_RESETN.POLARITY">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.S_AXI_RESETN.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.s_axi_resetn" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>intr</spirit:name>
      <spirit:displayName>Intr</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="interrupt" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="interrupt_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INTERRUPT</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>ip2intc_irpt</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>SENSITIVITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.INTR.SENSITIVITY">LEVEL_HIGH</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PortWidth</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.INTR.PortWidth">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.intr" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>CLK_IN1_D</spirit:name>
      <spirit:displayName>CLK_IN1_D</spirit:displayName>
      <spirit:description>Differential Clock input</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_N</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clk_in1_n</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_P</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clk_in1_p</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BOARD.ASSOCIATED_PARAM</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.CLK_IN1_D.BOARD.ASSOCIATED_PARAM">CLK_IN1_BOARD_INTERFACE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:enablement>
                <xilinx:presence>required</xilinx:presence>
              </xilinx:enablement>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CAN_DEBUG</spirit:name>
          <spirit:value spirit:format="bool" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK_IN1_D.CAN_DEBUG">false</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK_IN1_D.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.CLK_IN1_D" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_PRIM_SOURCE&apos;))=&quot;Differential_clock_capable_pin&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_PRIM_SOURCE&apos;))=&quot;Differential_non_clock_pin&quot;))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>CLK_IN2_D</spirit:name>
      <spirit:displayName>CLK_IN2_D</spirit:displayName>
      <spirit:description>Differential Clock input</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_N</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clk_in2_n</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_P</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clk_in2_p</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BOARD.ASSOCIATED_PARAM</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.CLK_IN2_D.BOARD.ASSOCIATED_PARAM">CLK_IN2_BOARD_INTERFACE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:enablement>
                <xilinx:presence>required</xilinx:presence>
              </xilinx:enablement>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CAN_DEBUG</spirit:name>
          <spirit:value spirit:format="bool" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK_IN2_D.CAN_DEBUG">false</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK_IN2_D.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.CLK_IN2_D" xilinx:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_SECONDARY_SOURCE&apos;))=&quot;Differential_clock_capable_pin&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_SECONDARY_SOURCE&apos;))=&quot;Differential_non_clock_pin&quot;)) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_INCLK_SWITCHOVER&apos;))=1))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>CLKFB_IN_D</spirit:name>
      <spirit:displayName>CLKFB_IN_D</spirit:displayName>
      <spirit:description>Differential Feedback Clock input</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_N</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clkfb_in_n</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_P</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clkfb_in_p</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>CAN_DEBUG</spirit:name>
          <spirit:value spirit:format="bool" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKFB_IN_D.CAN_DEBUG">false</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKFB_IN_D.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.CLKFB_IN_D" xilinx:dependency="((((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))!=&quot;FDBK_AUTO&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;))) or ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;)) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_CLKFB_IN_SIGNALING&apos;))=&quot;DIFF&quot;) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>CLKFB_OUT_D</spirit:name>
      <spirit:displayName>CLKFB_OUT_D</spirit:displayName>
      <spirit:description>Differential Feeback Clock Output</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="diff_clock_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_N</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clkfb_out_n</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_P</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clkfb_out_p</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>CAN_DEBUG</spirit:name>
          <spirit:value spirit:format="bool" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKFB_OUT_D.CAN_DEBUG">false</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKFB_OUT_D.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.CLKFB_OUT_D" xilinx:dependency="((((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))!=&quot;FDBK_AUTO&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;))) or ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;)) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_CLKFB_IN_SIGNALING&apos;))=&quot;DIFF&quot;) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>reset</spirit:name>
      <spirit:displayName>reset</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>reset</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.RESET.POLARITY">ACTIVE_HIGH</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>BOARD.ASSOCIATED_PARAM</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.RESET.BOARD.ASSOCIATED_PARAM">RESET_BOARD_INTERFACE</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.RESET.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.reset" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_RESET&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_RESET_LOW&apos;))=0) and (not spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;)))">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>resetn</spirit:name>
      <spirit:displayName>resetn</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>resetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.RESETN.POLARITY">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>BOARD.ASSOCIATED_PARAM</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.RESETN.BOARD.ASSOCIATED_PARAM">RESET_BOARD_INTERFACE</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.RESETN.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.resetn" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_RESET&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_RESET_LOW&apos;))=1) and (not spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;)))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>clock_CLK_IN1</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_IN1</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clk_in1</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_IN1.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_IN1.PHASE">0.000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_IN1.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_IN1.ASSOCIATED_BUSIF"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_IN1.ASSOCIATED_RESET"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_IN1.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>BOARD.ASSOCIATED_PARAM</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_IN1.BOARD.ASSOCIATED_PARAM">CLK_IN1_BOARD_INTERFACE</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>clock_CLK_OUT1</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK_OUT1</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clk_out1</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_OUT1.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_OUT1.PHASE">0.000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_OUT1.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_OUT1.ASSOCIATED_BUSIF"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_OUT1.ASSOCIATED_RESET"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.CLOCK_CLK_OUT1.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
  </spirit:busInterfaces>
  <spirit:model>
    <spirit:views>
      <spirit:view>
        <spirit:name>xilinx_elaborateports</spirit:name>
        <spirit:displayName>Elaborate Ports</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:elaborate.ports</spirit:envIdentifier>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:cde1e565</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_veriloginstantiationtemplate</spirit:name>
        <spirit:displayName>Verilog Instantiation Template</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis.template</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>clk_wiz_v6_0_2</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_veriloginstantiationtemplate_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Mon Jul 21 04:39:31 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:0b4434ea</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
    </spirit:views>
    <spirit:ports>
      <spirit:port>
        <spirit:name>s_axi_aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_aclk" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_aresetn" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awaddr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXI_ADDR_WIDTH&apos;)) - 1)">10</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awaddr" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awvalid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awready" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wdata</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXI_DATA_WIDTH&apos;)) - 1)">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wdata" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wstrb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXI_DATA_WIDTH&apos;)) div 8) - 1)">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wstrb" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wvalid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wready" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_bresp</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_bresp" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_bvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_bvalid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_bready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_bready" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_araddr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXI_ADDR_WIDTH&apos;)) - 1)">10</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_araddr" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arvalid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arready" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rdata</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXI_DATA_WIDTH&apos;)) - 1)">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rdata" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rresp</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rresp" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rvalid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rready" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_in1_p</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clk_in1_p" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_PRIM_SOURCE&apos;))=&quot;Differential_clock_capable_pin&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_PRIM_SOURCE&apos;))=&quot;Differential_non_clock_pin&quot;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_in1_n</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clk_in1_n" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_PRIM_SOURCE&apos;))=&quot;Differential_clock_capable_pin&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_PRIM_SOURCE&apos;))=&quot;Differential_non_clock_pin&quot;))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_in2_p</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clk_in2_p" xilinx:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_SECONDARY_SOURCE&apos;))=&quot;Differential_clock_capable_pin&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_SECONDARY_SOURCE&apos;))=&quot;Differential_non_clock_pin&quot;)) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_INCLK_SWITCHOVER&apos;))=1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_in2_n</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clk_in2_n" xilinx:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_SECONDARY_SOURCE&apos;))=&quot;Differential_clock_capable_pin&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_SECONDARY_SOURCE&apos;))=&quot;Differential_non_clock_pin&quot;)) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_INCLK_SWITCHOVER&apos;))=1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clkfb_in_p</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clkfb_in_p" xilinx:dependency="((((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))!=&quot;FDBK_AUTO&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;))) or ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;)) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_CLKFB_IN_SIGNALING&apos;))=&quot;DIFF&quot;) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clkfb_in_n</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clkfb_in_n" xilinx:dependency="((((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))!=&quot;FDBK_AUTO&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;))) or ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;)) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_CLKFB_IN_SIGNALING&apos;))=&quot;DIFF&quot;) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clkfb_out_p</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clkfb_out_p" xilinx:dependency="((((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))!=&quot;FDBK_AUTO&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;))) or ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;)) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_CLKFB_IN_SIGNALING&apos;))=&quot;DIFF&quot;) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clkfb_out_n</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clkfb_out_n" xilinx:dependency="((((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))!=&quot;FDBK_AUTO&quot;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;))) or ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;)) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FEEDBACK_SOURCE&apos;))=&quot;FDBK_AUTO_OFFCHIP&quot;) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_CLKFB_IN_SIGNALING&apos;))=&quot;DIFF&quot;) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MMCM_COMPENSATION&apos;))!=&quot;INTERNAL&quot;))))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>reset</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.reset" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_RESET&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_RESET_LOW&apos;))=0) and (not spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;)))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>resetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.resetn" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_RESET&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_RESET_LOW&apos;))=1) and (not spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_SELECTION&apos;)))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>ref_clk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.ref_clk" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_stop</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clk_stop" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_glitch</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clk_glitch" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>interrupt</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.interrupt" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_oor</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clk_oor" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>user_clk0</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.user_clk0" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_USER_CLOCK0&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_Enable_PLL0&apos;))=0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>user_clk1</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.user_clk1" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_USER_CLOCK1&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_Enable_PLL1&apos;))=0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>user_clk2</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.user_clk2" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_USER_CLOCK2&apos;))=1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>user_clk3</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.user_clk3" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR&apos;))=1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_ENABLE_USER_CLOCK3&apos;))=1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_in1</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>clk_out1</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>locked</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_elaborateports</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT2_USED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_USED" spirit:order="194">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USER_CLK_FREQ0</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USER_CLK_FREQ0" spirit:order="1194">100.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="string">
        <spirit:name>C_AUTO_PRIMITIVE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AUTO_PRIMITIVE" spirit:order="1195">MMCM</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USER_CLK_FREQ1</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USER_CLK_FREQ1" spirit:order="1195">100.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USER_CLK_FREQ2</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USER_CLK_FREQ2" spirit:order="1196">100.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USER_CLK_FREQ3</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USER_CLK_FREQ3" spirit:order="1197">100.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ENABLE_CLOCK_MONITOR</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_CLOCK_MONITOR" spirit:order="1200">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ENABLE_USER_CLOCK0</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_USER_CLOCK0" spirit:order="1201">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ENABLE_USER_CLOCK1</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_USER_CLOCK1" spirit:order="1202">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ENABLE_USER_CLOCK2</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_USER_CLOCK2" spirit:order="1203">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ENABLE_USER_CLOCK3</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_USER_CLOCK3" spirit:order="1204">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_Enable_PLL0</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_Enable_PLL0" spirit:order="1205">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_Enable_PLL1</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_Enable_PLL1" spirit:order="1206">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_REF_CLK_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_REF_CLK_FREQ" spirit:order="1209">100.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PRECISION</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRECISION" spirit:order="1209">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT3_USED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_USED" spirit:order="195">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT4_USED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_USED" spirit:order="196">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT5_USED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_USED" spirit:order="197">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT6_USED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_USED" spirit:order="198">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT7_USED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_USED" spirit:order="199">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_CLKOUT1_BAR</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_CLKOUT1_BAR" spirit:order="200">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_CLKOUT2_BAR</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_CLKOUT2_BAR" spirit:order="201">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_CLKOUT3_BAR</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_CLKOUT3_BAR" spirit:order="202">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_CLKOUT4_BAR</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_CLKOUT4_BAR" spirit:order="203">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>c_component_name</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.c_component_name">clk_wiz_0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLATFORM</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLATFORM" spirit:order="204">UNKNOWN</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_FREQ_SYNTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_FREQ_SYNTH" spirit:order="205">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_PHASE_ALIGNMENT</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_PHASE_ALIGNMENT" spirit:order="206">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PRIM_IN_JITTER</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIM_IN_JITTER" spirit:order="207">0.010</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SECONDARY_IN_JITTER</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SECONDARY_IN_JITTER" spirit:order="208">0.010</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_JITTER_SEL</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_JITTER_SEL" spirit:order="209">No_Jitter</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_MIN_POWER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_MIN_POWER" spirit:order="210">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_MIN_O_JITTER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_MIN_O_JITTER" spirit:order="211">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_MAX_I_JITTER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_MAX_I_JITTER" spirit:order="212">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_DYN_PHASE_SHIFT</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_DYN_PHASE_SHIFT" spirit:order="213">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_INCLK_SWITCHOVER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_INCLK_SWITCHOVER" spirit:order="214">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_DYN_RECONFIG</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_DYN_RECONFIG" spirit:order="215">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_SPREAD_SPECTRUM</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_SPREAD_SPECTRUM" spirit:order="216">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_FAST_SIMULATION</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_FAST_SIMULATION" spirit:order="217">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PRIMTYPE_SEL</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIMTYPE_SEL" spirit:order="218">AUTO</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_CLK_VALID</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_CLK_VALID" spirit:order="219">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PRIM_IN_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIM_IN_FREQ" spirit:order="220">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PRIM_IN_TIMEPERIOD</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIM_IN_TIMEPERIOD" spirit:order="220.001">10.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_IN_FREQ_UNITS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_IN_FREQ_UNITS" spirit:order="221">Units_MHz</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SECONDARY_IN_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SECONDARY_IN_FREQ" spirit:order="222">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SECONDARY_IN_TIMEPERIOD</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SECONDARY_IN_TIMEPERIOD" spirit:order="222.001">10.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_FEEDBACK_SOURCE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FEEDBACK_SOURCE" spirit:order="223">FDBK_AUTO</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PRIM_SOURCE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIM_SOURCE" spirit:order="224">Single_ended_clock_capable_pin</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PHASESHIFT_MODE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PHASESHIFT_MODE" spirit:order="2240">WAVEFORM</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SECONDARY_SOURCE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SECONDARY_SOURCE" spirit:order="225">Single_ended_clock_capable_pin</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_IN_SIGNALING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_IN_SIGNALING" spirit:order="226">SINGLE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_RESET</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_RESET" spirit:order="227">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_RESET_LOW</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_RESET_LOW" spirit:order="408">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_LOCKED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_LOCKED" spirit:order="228">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_INCLK_STOPPED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_INCLK_STOPPED" spirit:order="229">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_CLKFB_STOPPED</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_CLKFB_STOPPED" spirit:order="230">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_POWER_DOWN</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_POWER_DOWN" spirit:order="231">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_STATUS</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_STATUS" spirit:order="232">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_FREEZE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_FREEZE" spirit:order="233">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_NUM_OUT_CLKS</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_OUT_CLKS" spirit:order="234">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_DRIVES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_DRIVES" spirit:order="235">BUFG</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_DRIVES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_DRIVES" spirit:order="236">BUFG</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_DRIVES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_DRIVES" spirit:order="237">BUFG</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_DRIVES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_DRIVES" spirit:order="238">BUFG</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_DRIVES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_DRIVES" spirit:order="239">BUFG</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_DRIVES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_DRIVES" spirit:order="240">BUFG</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_DRIVES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_DRIVES" spirit:order="241">BUFG</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INCLK_SUM_ROW0</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INCLK_SUM_ROW0" spirit:order="242">Input Clock   Freq (MHz)    Input Jitter (UI)</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INCLK_SUM_ROW1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INCLK_SUM_ROW1" spirit:order="243">__primary_________100.000____________0.010</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INCLK_SUM_ROW2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INCLK_SUM_ROW2" spirit:order="244">no_secondary_input_clock </spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW0A</spirit:name>
        <spirit:displayName>C Outclk Sum Row0a</spirit:displayName>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW0A" spirit:order="245"> Output     Output      Phase    Duty Cycle   Pk-to-Pk     Phase</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW0B</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW0B" spirit:order="246">  Clock     Freq (MHz)  (degrees)    (%)     Jitter (ps)  Error (ps)</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW1" spirit:order="247">clk_out1____90.000______0.000______50.0______140.709____105.461</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW2" spirit:order="248">no_CLK_OUT2_output</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW3</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW3" spirit:order="249">no_CLK_OUT3_output</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW4</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW4" spirit:order="250">no_CLK_OUT4_output</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW5</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW5" spirit:order="251">no_CLK_OUT5_output</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW6</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW6" spirit:order="252">no_CLK_OUT6_output</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_OUTCLK_SUM_ROW7</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OUTCLK_SUM_ROW7" spirit:order="253">no_CLK_OUT7_output</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_REQUESTED_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_REQUESTED_OUT_FREQ" spirit:order="254">90</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_REQUESTED_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_REQUESTED_OUT_FREQ" spirit:order="255">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_REQUESTED_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_REQUESTED_OUT_FREQ" spirit:order="256">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_REQUESTED_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_REQUESTED_OUT_FREQ" spirit:order="257">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_REQUESTED_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_REQUESTED_OUT_FREQ" spirit:order="258">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_REQUESTED_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_REQUESTED_OUT_FREQ" spirit:order="259">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_REQUESTED_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_REQUESTED_OUT_FREQ" spirit:order="260">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_REQUESTED_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_REQUESTED_PHASE" spirit:order="261">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_REQUESTED_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_REQUESTED_PHASE" spirit:order="262">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_REQUESTED_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_REQUESTED_PHASE" spirit:order="263">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_REQUESTED_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_REQUESTED_PHASE" spirit:order="264">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_REQUESTED_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_REQUESTED_PHASE" spirit:order="265">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_REQUESTED_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_REQUESTED_PHASE" spirit:order="266">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_REQUESTED_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_REQUESTED_PHASE" spirit:order="267">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_REQUESTED_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_REQUESTED_DUTY_CYCLE" spirit:order="268">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_REQUESTED_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_REQUESTED_DUTY_CYCLE" spirit:order="269">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_REQUESTED_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_REQUESTED_DUTY_CYCLE" spirit:order="270">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_REQUESTED_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_REQUESTED_DUTY_CYCLE" spirit:order="271">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_REQUESTED_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_REQUESTED_DUTY_CYCLE" spirit:order="272">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_REQUESTED_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_REQUESTED_DUTY_CYCLE" spirit:order="273">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_REQUESTED_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_REQUESTED_DUTY_CYCLE" spirit:order="274">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_OUT_FREQ" spirit:order="275">90.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_OUT_FREQ" spirit:order="276">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_OUT_FREQ" spirit:order="277">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_OUT_FREQ" spirit:order="278">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_OUT_FREQ" spirit:order="279">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_OUT_FREQ" spirit:order="280">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_OUT_FREQ</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_OUT_FREQ" spirit:order="281">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_PHASE" spirit:order="282">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_PHASE" spirit:order="283">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_PHASE" spirit:order="284">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_PHASE" spirit:order="285">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_PHASE" spirit:order="286">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_PHASE" spirit:order="287">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_PHASE" spirit:order="288">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_DUTY_CYCLE" spirit:order="289">50.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_DUTY_CYCLE" spirit:order="290">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_DUTY_CYCLE" spirit:order="291">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_DUTY_CYCLE" spirit:order="292">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_DUTY_CYCLE" spirit:order="293">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_DUTY_CYCLE" spirit:order="294">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_DUTY_CYCLE" spirit:order="295">50.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_SAFE_CLOCK_STARTUP</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_SAFE_CLOCK_STARTUP" spirit:order="500">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_CLOCK_SEQUENCING</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_CLOCK_SEQUENCING" spirit:order="501">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT1_SEQUENCE_NUMBER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_SEQUENCE_NUMBER" spirit:order="502">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT2_SEQUENCE_NUMBER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_SEQUENCE_NUMBER" spirit:order="503">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT3_SEQUENCE_NUMBER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_SEQUENCE_NUMBER" spirit:order="504">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT4_SEQUENCE_NUMBER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_SEQUENCE_NUMBER" spirit:order="505">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT5_SEQUENCE_NUMBER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_SEQUENCE_NUMBER" spirit:order="506">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT6_SEQUENCE_NUMBER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_SEQUENCE_NUMBER" spirit:order="507">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_CLKOUT7_SEQUENCE_NUMBER</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_SEQUENCE_NUMBER" spirit:order="508">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_NOTES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_NOTES" spirit:order="296">None</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_BANDWIDTH</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_BANDWIDTH" spirit:order="297">OPTIMIZED</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKFBOUT_MULT_F</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKFBOUT_MULT_F" spirit:order="298">9.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKIN1_PERIOD</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKIN1_PERIOD" spirit:order="299">10.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKIN2_PERIOD</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKIN2_PERIOD" spirit:order="300">10.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT4_CASCADE</spirit:name>
        <spirit:value spirit:format="bool" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT4_CASCADE" spirit:order="301">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLOCK_HOLD</spirit:name>
        <spirit:value spirit:format="bool" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLOCK_HOLD" spirit:order="302">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_COMPENSATION</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_COMPENSATION" spirit:order="303">ZHOLD</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_MMCM_DIVCLK_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_DIVCLK_DIVIDE" spirit:order="304">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_REF_JITTER1</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_REF_JITTER1" spirit:order="305">0.010</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_REF_JITTER2</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_REF_JITTER2" spirit:order="306">0.010</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_STARTUP_WAIT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_STARTUP_WAIT" spirit:order="307">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT0_DIVIDE_F</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT0_DIVIDE_F" spirit:order="308">10.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT1_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT1_DIVIDE" spirit:order="309">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT2_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT2_DIVIDE" spirit:order="310">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT3_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT3_DIVIDE" spirit:order="311">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT4_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT4_DIVIDE" spirit:order="312">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT5_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT5_DIVIDE" spirit:order="313">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT6_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT6_DIVIDE" spirit:order="314">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT0_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT0_DUTY_CYCLE" spirit:order="315">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT1_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT1_DUTY_CYCLE" spirit:order="316">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT2_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT2_DUTY_CYCLE" spirit:order="317">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT3_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT3_DUTY_CYCLE" spirit:order="318">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT4_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT4_DUTY_CYCLE" spirit:order="319">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT5_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT5_DUTY_CYCLE" spirit:order="320">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT6_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT6_DUTY_CYCLE" spirit:order="321">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKFBOUT_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKFBOUT_PHASE" spirit:order="322">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT0_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT0_PHASE" spirit:order="323">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT1_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT1_PHASE" spirit:order="324">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT2_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT2_PHASE" spirit:order="325">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT3_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT3_PHASE" spirit:order="326">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT4_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT4_PHASE" spirit:order="327">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT5_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT5_PHASE" spirit:order="328">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT6_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT6_PHASE" spirit:order="329">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKFBOUT_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKFBOUT_USE_FINE_PS" spirit:order="330">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT0_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT0_USE_FINE_PS" spirit:order="331">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT1_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT1_USE_FINE_PS" spirit:order="332">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT2_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT2_USE_FINE_PS" spirit:order="333">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT3_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT3_USE_FINE_PS" spirit:order="334">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT4_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT4_USE_FINE_PS" spirit:order="335">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT5_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT5_USE_FINE_PS" spirit:order="336">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCM_CLKOUT6_USE_FINE_PS</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCM_CLKOUT6_USE_FINE_PS" spirit:order="337">FALSE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_NOTES</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_NOTES" spirit:order="338">No notes</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_BANDWIDTH</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_BANDWIDTH" spirit:order="339">OPTIMIZED</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLK_FEEDBACK</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLK_FEEDBACK" spirit:order="340">CLKFBOUT</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_CLKFBOUT_MULT</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKFBOUT_MULT" spirit:order="341">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKIN_PERIOD</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKIN_PERIOD" spirit:order="342">1.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_COMPENSATION</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_COMPENSATION" spirit:order="343">SYSTEM_SYNCHRONOUS</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_DIVCLK_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_DIVCLK_DIVIDE" spirit:order="344">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_REF_JITTER</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_REF_JITTER" spirit:order="345">0.010</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_CLKOUT0_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT0_DIVIDE" spirit:order="346">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_CLKOUT1_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT1_DIVIDE" spirit:order="347">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_CLKOUT2_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT2_DIVIDE" spirit:order="348">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_CLKOUT3_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT3_DIVIDE" spirit:order="349">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_CLKOUT4_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT4_DIVIDE" spirit:order="350">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PLL_CLKOUT5_DIVIDE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT5_DIVIDE" spirit:order="351">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT0_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT0_DUTY_CYCLE" spirit:order="352">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT1_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT1_DUTY_CYCLE" spirit:order="353">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT2_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT2_DUTY_CYCLE" spirit:order="354">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT3_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT3_DUTY_CYCLE" spirit:order="355">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT4_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT4_DUTY_CYCLE" spirit:order="356">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT5_DUTY_CYCLE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT5_DUTY_CYCLE" spirit:order="357">0.500</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKFBOUT_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKFBOUT_PHASE" spirit:order="358">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT0_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT0_PHASE" spirit:order="359">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT1_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT1_PHASE" spirit:order="360">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT2_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT2_PHASE" spirit:order="361">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT3_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT3_PHASE" spirit:order="362">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT4_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT4_PHASE" spirit:order="363">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLL_CLKOUT5_PHASE</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLL_CLKOUT5_PHASE" spirit:order="364">0.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLOCK_MGR_TYPE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLOCK_MGR_TYPE" spirit:order="365">NA</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_OVERRIDE_MMCM</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OVERRIDE_MMCM" spirit:order="366">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_OVERRIDE_PLL</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_OVERRIDE_PLL" spirit:order="367">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PRIMARY_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIMARY_PORT" spirit:order="368">clk_in1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SECONDARY_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SECONDARY_PORT" spirit:order="369">clk_in2</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_OUT1_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_OUT1_PORT" spirit:order="370">clk_out1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_OUT2_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_OUT2_PORT" spirit:order="371">clk_out2</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_OUT3_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_OUT3_PORT" spirit:order="372">clk_out3</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_OUT4_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_OUT4_PORT" spirit:order="373">clk_out4</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_OUT5_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_OUT5_PORT" spirit:order="374">clk_out5</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_OUT6_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_OUT6_PORT" spirit:order="375">clk_out6</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_OUT7_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_OUT7_PORT" spirit:order="376">clk_out7</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_RESET_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_RESET_PORT" spirit:order="377">reset</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_LOCKED_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_LOCKED_PORT" spirit:order="378">locked</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_IN_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_IN_PORT" spirit:order="379">clkfb_in</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_IN_P_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_IN_P_PORT" spirit:order="380">clkfb_in_p</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_IN_N_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_IN_N_PORT" spirit:order="381">clkfb_in_n</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_OUT_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_OUT_PORT" spirit:order="382">clkfb_out</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_OUT_P_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_OUT_P_PORT" spirit:order="383">clkfb_out_p</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_OUT_N_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_OUT_N_PORT" spirit:order="384">clkfb_out_n</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_POWER_DOWN_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_POWER_DOWN_PORT" spirit:order="385">power_down</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DADDR_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DADDR_PORT" spirit:order="386">daddr</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DCLK_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DCLK_PORT" spirit:order="387">dclk</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DRDY_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DRDY_PORT" spirit:order="388">drdy</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DWE_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DWE_PORT" spirit:order="389">dwe</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIN_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIN_PORT" spirit:order="390">din</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DOUT_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DOUT_PORT" spirit:order="391">dout</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DEN_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DEN_PORT" spirit:order="392">den</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PSCLK_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PSCLK_PORT" spirit:order="393">psclk</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PSEN_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PSEN_PORT" spirit:order="394">psen</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PSINCDEC_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PSINCDEC_PORT" spirit:order="395">psincdec</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PSDONE_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PSDONE_PORT" spirit:order="396">psdone</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_VALID_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_VALID_PORT" spirit:order="397">CLK_VALID</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_STATUS_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_STATUS_PORT" spirit:order="398">STATUS</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLK_IN_SEL_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLK_IN_SEL_PORT" spirit:order="399">clk_in_sel</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INPUT_CLK_STOPPED_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INPUT_CLK_STOPPED_PORT" spirit:order="400">input_clk_stopped</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFB_STOPPED_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFB_STOPPED_PORT" spirit:order="401">clkfb_stopped</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKIN1_JITTER_PS</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKIN1_JITTER_PS" spirit:order="402">100.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKIN2_JITTER_PS</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKIN2_JITTER_PS" spirit:order="403">100.0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PRIMITIVE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIMITIVE" spirit:order="404">PLL</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SS_MODE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SS_MODE" spirit:order="405">CENTER_HIGH</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_SS_MOD_PERIOD</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SS_MOD_PERIOD" spirit:order="406">4000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SS_MOD_TIME</spirit:name>
        <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SS_MOD_TIME" spirit:order="406.001">0.004</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_CDDC</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_CDDC" spirit:order="407">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CDDCDONE_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CDDCDONE_PORT" spirit:order="408">cddcdone</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CDDCREQ_PORT</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CDDCREQ_PORT" spirit:order="409">cddcreq</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUTPHY_MODE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUTPHY_MODE" spirit:order="410">VCO</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ENABLE_CLKOUTPHY</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_CLKOUTPHY" spirit:order="411">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_INTERFACE_SELECTION</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INTERFACE_SELECTION" spirit:order="412">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_S_AXI_ADDR_WIDTH</spirit:name>
        <spirit:displayName>C S Axi Addr Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_AXI_ADDR_WIDTH" spirit:order="215" spirit:minimum="2" spirit:maximum="32" spirit:rangeType="long">11</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_S_AXI_DATA_WIDTH</spirit:name>
        <spirit:displayName>C S Axi Data Width</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_AXI_DATA_WIDTH" spirit:order="216" spirit:minimum="32" spirit:maximum="128" spirit:rangeType="long">32</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_POWER_REG</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_POWER_REG" spirit:order="409">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT0_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT0_1" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT0_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT0_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_1" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_2" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_1" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_1" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_1" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_1" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_1" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFBOUT_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFBOUT_1" spirit:order="410">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKFBOUT_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKFBOUT_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVCLK</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVCLK" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_LOCK_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_LOCK_1" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_LOCK_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_LOCK_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_LOCK_3</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_LOCK_3" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_FILTER_1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FILTER_1" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_FILTER_2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FILTER_2" spirit:order="411">0000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVIDE1_AUTO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVIDE1_AUTO" spirit:order="411">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVIDE2_AUTO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVIDE2_AUTO" spirit:order="411">0.9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVIDE3_AUTO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVIDE3_AUTO" spirit:order="411">0.9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVIDE4_AUTO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVIDE4_AUTO" spirit:order="411">0.9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVIDE5_AUTO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVIDE5_AUTO" spirit:order="411">0.9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVIDE6_AUTO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVIDE6_AUTO" spirit:order="411">0.9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DIVIDE7_AUTO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DIVIDE7_AUTO" spirit:order="411">0.9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLLBUFGCEDIV</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLLBUFGCEDIV" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLLBUFGCEDIV1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLLBUFGCEDIV1" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLLBUFGCEDIV2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLLBUFGCEDIV2" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLLBUFGCEDIV3</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLLBUFGCEDIV3" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_PLLBUFGCEDIV4</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PLLBUFGCEDIV4" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV1</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV1" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV2</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV2" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV3</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV3" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV4</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV4" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV5</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV5" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV6</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV6" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_MMCMBUFGCEDIV7</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MMCMBUFGCEDIV7" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_MATCHED_ROUTING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_MATCHED_ROUTING" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_MATCHED_ROUTING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_MATCHED_ROUTING" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_MATCHED_ROUTING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_MATCHED_ROUTING" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_MATCHED_ROUTING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_MATCHED_ROUTING" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_MATCHED_ROUTING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_MATCHED_ROUTING" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_MATCHED_ROUTING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_MATCHED_ROUTING" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT7_MATCHED_ROUTING</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT7_MATCHED_ROUTING" spirit:order="411">false</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT0_ACTUAL_FREQ</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT0_ACTUAL_FREQ" spirit:order="711">90.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT1_ACTUAL_FREQ</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT1_ACTUAL_FREQ" spirit:order="712">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT2_ACTUAL_FREQ</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT2_ACTUAL_FREQ" spirit:order="713">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT3_ACTUAL_FREQ</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT3_ACTUAL_FREQ" spirit:order="714">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT4_ACTUAL_FREQ</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT4_ACTUAL_FREQ" spirit:order="715">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT5_ACTUAL_FREQ</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT5_ACTUAL_FREQ" spirit:order="716">100.000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CLKOUT6_ACTUAL_FREQ</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CLKOUT6_ACTUAL_FREQ" spirit:order="717">100.000</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_list_1d3de01d</spirit:name>
      <spirit:enumeration>WAVEFORM</spirit:enumeration>
      <spirit:enumeration>LATENCY</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_876bfc32</spirit:name>
      <spirit:enumeration>UI</spirit:enumeration>
      <spirit:enumeration>PS</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_a9bdfce0</spirit:name>
      <spirit:enumeration>LOW</spirit:enumeration>
      <spirit:enumeration>HIGH</spirit:enumeration>
      <spirit:enumeration>OPTIMIZED</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_ac75ef1e</spirit:name>
      <spirit:enumeration>Custom</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_b9d38208</spirit:name>
      <spirit:enumeration>CLKFBOUT</spirit:enumeration>
      <spirit:enumeration>CLKOUT0</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_e099fe6c</spirit:name>
      <spirit:enumeration>MMCM</spirit:enumeration>
      <spirit:enumeration>PLL</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_035ca1c3</spirit:name>
      <spirit:enumeration spirit:text="SYSTEM SYNCHRONOUS">SYSTEM_SYNCHRONOUS</spirit:enumeration>
      <spirit:enumeration spirit:text="SOURCE SYNCHRONOUS">SOURCE_SYNCHRONOUS</spirit:enumeration>
      <spirit:enumeration spirit:text="INTERNAL">INTERNAL</spirit:enumeration>
      <spirit:enumeration spirit:text="EXTERNAL">EXTERNAL</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_0920eb1b</spirit:name>
      <spirit:enumeration spirit:text="Custom">Custom</spirit:enumeration>
      <spirit:enumeration spirit:text="sys diff clock">sys_diff_clock</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_11d71346</spirit:name>
      <spirit:enumeration spirit:text="Single ended clock capable pin">Single_ended_clock_capable_pin</spirit:enumeration>
      <spirit:enumeration spirit:text="Differential clock capable pin">Differential_clock_capable_pin</spirit:enumeration>
      <spirit:enumeration spirit:text="Global buffer">Global_buffer</spirit:enumeration>
      <spirit:enumeration spirit:text="No buffer">No_buffer</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_15c806d5</spirit:name>
      <spirit:enumeration spirit:text="Automatic Control On-Chip">FDBK_AUTO</spirit:enumeration>
      <spirit:enumeration spirit:text="Automatic Control Off-Chip">FDBK_AUTO_OFFCHIP</spirit:enumeration>
      <spirit:enumeration spirit:text="User-Controlled On-Chip">FDBK_ONCHIP</spirit:enumeration>
      <spirit:enumeration spirit:text="User-Controlled Off-Chip">FDBK_OFFCHIP</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_340369e0</spirit:name>
      <spirit:enumeration spirit:text="Custom">Custom</spirit:enumeration>
      <spirit:enumeration spirit:text="sys clock">sys_clock</spirit:enumeration>
      <spirit:enumeration spirit:text="sys diff clock">sys_diff_clock</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_3c2d3ec7</spirit:name>
      <spirit:enumeration spirit:text="Single-ended">SINGLE</spirit:enumeration>
      <spirit:enumeration spirit:text="Differential">DIFF</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_502d9f23</spirit:name>
      <spirit:enumeration spirit:text="ZHOLD">ZHOLD</spirit:enumeration>
      <spirit:enumeration spirit:text="EXTERNAL">EXTERNAL</spirit:enumeration>
      <spirit:enumeration spirit:text="INTERNAL">INTERNAL</spirit:enumeration>
      <spirit:enumeration spirit:text="BUF IN">BUF_IN</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_66e4c81f</spirit:name>
      <spirit:enumeration spirit:text="BUFG">BUFG</spirit:enumeration>
      <spirit:enumeration spirit:text="BUFH">BUFH</spirit:enumeration>
      <spirit:enumeration spirit:text="BUFGCE">BUFGCE</spirit:enumeration>
      <spirit:enumeration spirit:text="BUFHCE">BUFHCE</spirit:enumeration>
      <spirit:enumeration spirit:text="No buffer">No_buffer</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_77d3d587</spirit:name>
      <spirit:enumeration spirit:text="MMCM">MMCM</spirit:enumeration>
      <spirit:enumeration spirit:text="PLL">PLL</spirit:enumeration>
      <spirit:enumeration spirit:text="BUFGCE DIV">BUFGCE_DIV</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_8b28f1f7</spirit:name>
      <spirit:enumeration spirit:text="AXI4Lite">Enable_AXI</spirit:enumeration>
      <spirit:enumeration spirit:text="DRP">Enable_DRP</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_8eea9b32</spirit:name>
      <spirit:enumeration spirit:text="Units MHz">Units_MHz</spirit:enumeration>
      <spirit:enumeration spirit:text="Units ns">Units_ns</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_a4fbc00c</spirit:name>
      <spirit:enumeration spirit:text="Active High">ACTIVE_HIGH</spirit:enumeration>
      <spirit:enumeration spirit:text="Active Low">ACTIVE_LOW</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_a8642b4c</spirit:name>
      <spirit:enumeration spirit:text="Balanced">No_Jitter</spirit:enumeration>
      <spirit:enumeration spirit:text="Minimize Output Jitter">Min_O_Jitter</spirit:enumeration>
      <spirit:enumeration spirit:text="Maximize Input Jitter filtering">Max_I_Jitter</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_c5ef7212</spirit:name>
      <spirit:enumeration spirit:text="Units UI">Units_UI</spirit:enumeration>
      <spirit:enumeration spirit:text="Units ps">Units_ps</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_e1c87518</spirit:name>
      <spirit:enumeration spirit:text="Primary Clock">REL_PRIMARY</spirit:enumeration>
      <spirit:enumeration spirit:text="Secondary Clock">REL_SECONDARY</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_f4e10086</spirit:name>
      <spirit:enumeration spirit:text="CENTER HIGH">CENTER_HIGH</spirit:enumeration>
      <spirit:enumeration spirit:text="CENTER LOW">CENTER_LOW</spirit:enumeration>
      <spirit:enumeration spirit:text="DOWN HIGH">DOWN_HIGH</spirit:enumeration>
      <spirit:enumeration spirit:text="DOWN LOW">DOWN_LOW</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_f669c2f5</spirit:name>
      <spirit:enumeration spirit:text="Frequency">frequency</spirit:enumeration>
      <spirit:enumeration spirit:text="Time">Time</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:fileSets>
    <spirit:fileSet>
      <spirit:name>xilinx_veriloginstantiationtemplate_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>clk_wiz_0.veo</spirit:name>
        <spirit:userFileType>verilogTemplate</spirit:userFileType>
      </spirit:file>
    </spirit:fileSet>
  </spirit:fileSets>
  <spirit:description>The Clocking Wizard creates an HDL file (Verilog or VHDL) that contains a clocking circuit customized to the user&apos;s clocking requirements.</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">clk_wiz_0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USER_CLK_FREQ0</spirit:name>
      <spirit:displayName>User Frequency(MHz)</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.USER_CLK_FREQ0" spirit:order="15200">100.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USER_CLK_FREQ1</spirit:name>
      <spirit:displayName>User Frequency(MHz)</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.USER_CLK_FREQ1" spirit:order="15200">100.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USER_CLK_FREQ2</spirit:name>
      <spirit:displayName>User Frequency(MHz)</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.USER_CLK_FREQ2" spirit:order="15200" spirit:minimum="1" spirit:maximum="300">100.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USER_CLK_FREQ3</spirit:name>
      <spirit:displayName>User Frequency(MHz)</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.USER_CLK_FREQ3" spirit:order="15200" spirit:minimum="1" spirit:maximum="300">100.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_CLOCK_MONITOR</spirit:name>
      <spirit:displayName>Enable Clock Monitoring</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_CLOCK_MONITOR" spirit:order="10.1">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_USER_CLOCK0</spirit:name>
      <spirit:displayName>User Clock</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_USER_CLOCK0" spirit:order="1090">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_USER_CLOCK1</spirit:name>
      <spirit:displayName>User Clock</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_USER_CLOCK1" spirit:order="1090">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_USER_CLOCK2</spirit:name>
      <spirit:displayName>User Clock</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_USER_CLOCK2" spirit:order="1090">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_USER_CLOCK3</spirit:name>
      <spirit:displayName>User Clock</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_USER_CLOCK3" spirit:order="1090">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Enable_PLL0</spirit:name>
      <spirit:displayName>User Clock</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Enable_PLL0" spirit:order="1090">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Enable_PLL1</spirit:name>
      <spirit:displayName>User Clock</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Enable_PLL1" spirit:order="1090">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>REF_CLK_FREQ</spirit:name>
      <spirit:displayName>Reference Frequency(MHz)</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.REF_CLK_FREQ" spirit:order="15300" spirit:minimum="1" spirit:maximum="300">100.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRECISION</spirit:name>
      <spirit:displayName>Tolerance(MHz)</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PRECISION" spirit:order="15400" spirit:minimum="1" spirit:maximum="100">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIMITIVE</spirit:name>
      <spirit:displayName>Primitive</spirit:displayName>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PRIMITIVE" spirit:choiceRef="choice_list_e099fe6c" spirit:order="2">PLL</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIMTYPE_SEL</spirit:name>
      <spirit:displayName>Primtype Sel</spirit:displayName>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PRIMTYPE_SEL" spirit:order="3">mmcm_adv</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLOCK_MGR_TYPE</spirit:name>
      <spirit:displayName>Clock Mgr Type</spirit:displayName>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLOCK_MGR_TYPE" spirit:order="410">auto</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_FREQ_SYNTH</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_FREQ_SYNTH" spirit:order="6" spirit:configGroups="0 NoDisplay">true</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_SPREAD_SPECTRUM</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_SPREAD_SPECTRUM" spirit:order="7" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_PHASE_ALIGNMENT</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_PHASE_ALIGNMENT" spirit:order="8" spirit:configGroups="0 NoDisplay">true</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_MIN_POWER</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_MIN_POWER" spirit:order="9" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_DYN_PHASE_SHIFT</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_DYN_PHASE_SHIFT" spirit:order="10" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_DYN_RECONFIG</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_DYN_RECONFIG" spirit:order="11" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>JITTER_SEL</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.JITTER_SEL" spirit:choiceRef="choice_pairs_a8642b4c" spirit:order="13" spirit:configGroups="0 NoDisplay">No_Jitter</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIM_IN_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PRIM_IN_FREQ" spirit:order="14.401" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIM_IN_TIMEPERIOD</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PRIM_IN_TIMEPERIOD" spirit:order="14.9" spirit:configGroups="0 NoDisplay">10.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN_FREQ_UNITS</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.IN_FREQ_UNITS" spirit:choiceRef="choice_pairs_8eea9b32" spirit:order="15" spirit:configGroups="0 NoDisplay">Units_MHz</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PHASESHIFT_MODE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PHASESHIFT_MODE" spirit:choiceRef="choice_list_1d3de01d" spirit:order="116" spirit:configGroups="0 NoDisplay">WAVEFORM</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>IN_JITTER_UNITS</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.IN_JITTER_UNITS" spirit:choiceRef="choice_pairs_c5ef7212" spirit:order="16" spirit:configGroups="0 NoDisplay">Units_UI</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>RELATIVE_INCLK</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.RELATIVE_INCLK" spirit:choiceRef="choice_pairs_e1c87518" spirit:order="17" spirit:configGroups="0 NoDisplay">REL_PRIMARY</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_INCLK_SWITCHOVER</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_INCLK_SWITCHOVER" spirit:order="13.9" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SECONDARY_IN_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.SECONDARY_IN_FREQ" spirit:order="21.3" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SECONDARY_IN_TIMEPERIOD</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.SECONDARY_IN_TIMEPERIOD" spirit:order="21.299" spirit:configGroups="0 NoDisplay">10.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SECONDARY_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.SECONDARY_PORT" spirit:order="20" spirit:configGroups="0 NoDisplay">clk_in2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SECONDARY_SOURCE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.SECONDARY_SOURCE" spirit:choiceRef="choice_pairs_11d71346" spirit:order="21" spirit:configGroups="0 NoDisplay">Single_ended_clock_capable_pin</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>JITTER_OPTIONS</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.JITTER_OPTIONS" spirit:choiceRef="choice_list_876bfc32" spirit:order="22" spirit:configGroups="0 NoDisplay">UI</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKIN1_UI_JITTER</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKIN1_UI_JITTER" spirit:order="23" spirit:configGroups="0 NoDisplay">0.010</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKIN2_UI_JITTER</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKIN2_UI_JITTER" spirit:order="24" spirit:configGroups="0 NoDisplay">0.010</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIM_IN_JITTER</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PRIM_IN_JITTER" spirit:order="25" spirit:configGroups="0 NoDisplay">0.010</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SECONDARY_IN_JITTER</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.SECONDARY_IN_JITTER" spirit:order="26" spirit:configGroups="0 NoDisplay">0.010</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKIN1_JITTER_PS</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKIN1_JITTER_PS" spirit:order="27" spirit:configGroups="0 NoDisplay">100.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKIN2_JITTER_PS</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKIN2_JITTER_PS" spirit:order="28" spirit:configGroups="0 NoDisplay">100.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_USED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_USED" spirit:order="4" spirit:configGroups="0 NoDisplay">true</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_USED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_USED" spirit:order="29" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_USED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_USED" spirit:order="30" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_USED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_USED" spirit:order="31" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_USED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_USED" spirit:order="32" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_USED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_USED" spirit:order="33" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_USED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_USED" spirit:order="34" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_OUT_CLKS</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_OUT_CLKS" spirit:order="407" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT1_USE_FINE_PS_GUI</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT1_USE_FINE_PS_GUI" spirit:order="36" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT2_USE_FINE_PS_GUI</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT2_USE_FINE_PS_GUI" spirit:order="37" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT3_USE_FINE_PS_GUI</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT3_USE_FINE_PS_GUI" spirit:order="38" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT4_USE_FINE_PS_GUI</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT4_USE_FINE_PS_GUI" spirit:order="39" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT5_USE_FINE_PS_GUI</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT5_USE_FINE_PS_GUI" spirit:order="40" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT6_USE_FINE_PS_GUI</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT6_USE_FINE_PS_GUI" spirit:order="41" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT7_USE_FINE_PS_GUI</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT7_USE_FINE_PS_GUI" spirit:order="42" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIMARY_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PRIMARY_PORT" spirit:order="43" spirit:configGroups="0 NoDisplay">clk_in1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT1_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT1_PORT" spirit:order="44" spirit:configGroups="0 NoDisplay">clk_out1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT2_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT2_PORT" spirit:order="45" spirit:configGroups="0 NoDisplay">clk_out2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT3_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT3_PORT" spirit:order="46" spirit:configGroups="0 NoDisplay">clk_out3</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT4_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT4_PORT" spirit:order="47" spirit:configGroups="0 NoDisplay">clk_out4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT5_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT5_PORT" spirit:order="48" spirit:configGroups="0 NoDisplay">clk_out5</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT6_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT6_PORT" spirit:order="49" spirit:configGroups="0 NoDisplay">clk_out6</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_OUT7_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_OUT7_PORT" spirit:order="50" spirit:configGroups="0 NoDisplay">clk_out7</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DADDR_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DADDR_PORT" spirit:order="51" spirit:configGroups="0 NoDisplay">daddr</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DCLK_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DCLK_PORT" spirit:order="52" spirit:configGroups="0 NoDisplay">dclk</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DRDY_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DRDY_PORT" spirit:order="53" spirit:configGroups="0 NoDisplay">drdy</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DWE_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DWE_PORT" spirit:order="54" spirit:configGroups="0 NoDisplay">dwe</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DIN_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DIN_PORT" spirit:order="55" spirit:configGroups="0 NoDisplay">din</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DOUT_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DOUT_PORT" spirit:order="56" spirit:configGroups="0 NoDisplay">dout</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DEN_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DEN_PORT" spirit:order="57" spirit:configGroups="0 NoDisplay">den</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PSCLK_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PSCLK_PORT" spirit:order="58" spirit:configGroups="0 NoDisplay">psclk</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PSEN_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PSEN_PORT" spirit:order="59" spirit:configGroups="0 NoDisplay">psen</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PSINCDEC_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PSINCDEC_PORT" spirit:order="60" spirit:configGroups="0 NoDisplay">psincdec</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PSDONE_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PSDONE_PORT" spirit:order="61" spirit:configGroups="0 NoDisplay">psdone</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_REQUESTED_OUT_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_REQUESTED_OUT_FREQ" spirit:order="62" spirit:configGroups="0 NoDisplay">90</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_REQUESTED_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_REQUESTED_PHASE" spirit:order="63" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_REQUESTED_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_REQUESTED_DUTY_CYCLE" spirit:order="64" spirit:configGroups="0 NoDisplay">50.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_REQUESTED_OUT_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_REQUESTED_OUT_FREQ" spirit:order="65" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_REQUESTED_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_REQUESTED_PHASE" spirit:order="66" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_REQUESTED_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_REQUESTED_DUTY_CYCLE" spirit:order="67" spirit:configGroups="0 NoDisplay">50.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_REQUESTED_OUT_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_REQUESTED_OUT_FREQ" spirit:order="68" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_REQUESTED_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_REQUESTED_PHASE" spirit:order="69" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_REQUESTED_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_REQUESTED_DUTY_CYCLE" spirit:order="70" spirit:configGroups="0 NoDisplay">50.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_REQUESTED_OUT_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_REQUESTED_OUT_FREQ" spirit:order="71" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_REQUESTED_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_REQUESTED_PHASE" spirit:order="72" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_REQUESTED_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_REQUESTED_DUTY_CYCLE" spirit:order="73" spirit:configGroups="0 NoDisplay">50.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_REQUESTED_OUT_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_REQUESTED_OUT_FREQ" spirit:order="74" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_REQUESTED_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_REQUESTED_PHASE" spirit:order="75" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_REQUESTED_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_REQUESTED_DUTY_CYCLE" spirit:order="76" spirit:configGroups="0 NoDisplay">50.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_REQUESTED_OUT_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_REQUESTED_OUT_FREQ" spirit:order="77" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_REQUESTED_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_REQUESTED_PHASE" spirit:order="78" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_REQUESTED_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_REQUESTED_DUTY_CYCLE" spirit:order="79" spirit:configGroups="0 NoDisplay">50.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_REQUESTED_OUT_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_REQUESTED_OUT_FREQ" spirit:order="80" spirit:configGroups="0 NoDisplay">100.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_REQUESTED_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_REQUESTED_PHASE" spirit:order="81" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_REQUESTED_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_REQUESTED_DUTY_CYCLE" spirit:order="82" spirit:configGroups="0 NoDisplay">50.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_MAX_I_JITTER</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_MAX_I_JITTER" spirit:order="83" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_MIN_O_JITTER</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_MIN_O_JITTER" spirit:order="84" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_MATCHED_ROUTING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_MATCHED_ROUTING" spirit:order="984" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_MATCHED_ROUTING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_MATCHED_ROUTING" spirit:order="985" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_MATCHED_ROUTING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_MATCHED_ROUTING" spirit:order="986" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_MATCHED_ROUTING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_MATCHED_ROUTING" spirit:order="987" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_MATCHED_ROUTING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_MATCHED_ROUTING" spirit:order="988" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_MATCHED_ROUTING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_MATCHED_ROUTING" spirit:order="989" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_MATCHED_ROUTING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_MATCHED_ROUTING" spirit:order="990" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIM_SOURCE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PRIM_SOURCE" spirit:choiceRef="choice_pairs_11d71346" spirit:order="14.1" spirit:configGroups="0 NoDisplay">Single_ended_clock_capable_pin</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_DRIVES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_DRIVES" spirit:choiceRef="choice_pairs_66e4c81f" spirit:order="86" spirit:configGroups="0 NoDisplay">BUFG</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_DRIVES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_DRIVES" spirit:choiceRef="choice_pairs_66e4c81f" spirit:order="87" spirit:configGroups="0 NoDisplay">BUFG</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_DRIVES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_DRIVES" spirit:choiceRef="choice_pairs_66e4c81f" spirit:order="88" spirit:configGroups="0 NoDisplay">BUFG</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_DRIVES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_DRIVES" spirit:choiceRef="choice_pairs_66e4c81f" spirit:order="89" spirit:configGroups="0 NoDisplay">BUFG</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_DRIVES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_DRIVES" spirit:choiceRef="choice_pairs_66e4c81f" spirit:order="90" spirit:configGroups="0 NoDisplay">BUFG</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_DRIVES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_DRIVES" spirit:choiceRef="choice_pairs_66e4c81f" spirit:order="91" spirit:configGroups="0 NoDisplay">BUFG</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_DRIVES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_DRIVES" spirit:choiceRef="choice_pairs_66e4c81f" spirit:order="92" spirit:configGroups="0 NoDisplay">BUFG</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>FEEDBACK_SOURCE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.FEEDBACK_SOURCE" spirit:choiceRef="choice_pairs_15c806d5" spirit:order="93" spirit:configGroups="0 NoDisplay">FDBK_AUTO</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_IN_SIGNALING</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_IN_SIGNALING" spirit:choiceRef="choice_pairs_3c2d3ec7" spirit:order="94" spirit:configGroups="0 NoDisplay">SINGLE</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_IN_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_IN_PORT" spirit:order="95" spirit:configGroups="0 NoDisplay">clkfb_in</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_IN_P_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_IN_P_PORT" spirit:order="96" spirit:configGroups="0 NoDisplay">clkfb_in_p</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_IN_N_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_IN_N_PORT" spirit:order="97" spirit:configGroups="0 NoDisplay">clkfb_in_n</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_OUT_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_OUT_PORT" spirit:order="98" spirit:configGroups="0 NoDisplay">clkfb_out</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_OUT_P_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_OUT_P_PORT" spirit:order="99" spirit:configGroups="0 NoDisplay">clkfb_out_p</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_OUT_N_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_OUT_N_PORT" spirit:order="100" spirit:configGroups="0 NoDisplay">clkfb_out_n</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLATFORM</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PLATFORM" spirit:order="101" spirit:configGroups="0 NoDisplay">UNKNOWN</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SUMMARY_STRINGS</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.SUMMARY_STRINGS" spirit:order="102" spirit:configGroups="0 NoDisplay">empty</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_LOCKED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_LOCKED" spirit:order="103" spirit:configGroups="0 NoDisplay">true</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CALC_DONE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CALC_DONE" spirit:order="104" spirit:configGroups="0 NoDisplay">empty</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_RESET</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_RESET" spirit:order="105" spirit:configGroups="0 NoDisplay">true</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_POWER_DOWN</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_POWER_DOWN" spirit:order="106" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_STATUS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_STATUS" spirit:order="107" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_FREEZE</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_FREEZE" spirit:order="108" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_CLK_VALID</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_CLK_VALID" spirit:order="109" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_INCLK_STOPPED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_INCLK_STOPPED" spirit:order="110" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_CLKFB_STOPPED</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_CLKFB_STOPPED" spirit:order="111" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>RESET_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.RESET_PORT" spirit:order="409" spirit:configGroups="0 NoDisplay">reset</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>LOCKED_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.LOCKED_PORT" spirit:order="113" spirit:configGroups="0 NoDisplay">locked</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>POWER_DOWN_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.POWER_DOWN_PORT" spirit:order="114" spirit:configGroups="0 NoDisplay">power_down</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_VALID_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_VALID_PORT" spirit:order="115" spirit:configGroups="0 NoDisplay">CLK_VALID</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>STATUS_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.STATUS_PORT" spirit:order="116" spirit:configGroups="0 NoDisplay">STATUS</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_IN_SEL_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_IN_SEL_PORT" spirit:order="117" spirit:configGroups="0 NoDisplay">clk_in_sel</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>INPUT_CLK_STOPPED_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.INPUT_CLK_STOPPED_PORT" spirit:order="118" spirit:configGroups="0 NoDisplay">input_clk_stopped</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKFB_STOPPED_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLKFB_STOPPED_PORT" spirit:order="119" spirit:configGroups="0 NoDisplay">clkfb_stopped</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SS_MODE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.SS_MODE" spirit:choiceRef="choice_pairs_f4e10086" spirit:order="120" spirit:configGroups="0 NoDisplay">CENTER_HIGH</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SS_MOD_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.SS_MOD_FREQ" spirit:order="121" spirit:configGroups="0 NoDisplay">250</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SS_MOD_TIME</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.SS_MOD_TIME" spirit:order="121.001" spirit:configGroups="0 NoDisplay">0.004</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>OVERRIDE_MMCM</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.OVERRIDE_MMCM" spirit:order="122" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_NOTES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_NOTES" spirit:order="123" spirit:configGroups="0 NoDisplay">None</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_DIVCLK_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_DIVCLK_DIVIDE" spirit:order="124" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="56" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_BANDWIDTH</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_BANDWIDTH" spirit:choiceRef="choice_list_a9bdfce0" spirit:order="125" spirit:configGroups="0 NoDisplay">OPTIMIZED</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKFBOUT_MULT_F</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKFBOUT_MULT_F" spirit:order="126" spirit:configGroups="0 NoDisplay">9</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKFBOUT_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKFBOUT_PHASE" spirit:order="127" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKFBOUT_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKFBOUT_USE_FINE_PS" spirit:order="128" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKIN1_PERIOD</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKIN1_PERIOD" spirit:order="129" spirit:configGroups="0 NoDisplay">10.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKIN2_PERIOD</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKIN2_PERIOD" spirit:order="130" spirit:configGroups="0 NoDisplay">10.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT4_CASCADE</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT4_CASCADE" spirit:order="131" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLOCK_HOLD</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLOCK_HOLD" spirit:order="132" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_COMPENSATION</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_COMPENSATION" spirit:choiceRef="choice_pairs_502d9f23" spirit:order="133" spirit:configGroups="0 NoDisplay">ZHOLD</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_REF_JITTER1</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_REF_JITTER1" spirit:order="134" spirit:configGroups="0 NoDisplay">0.010</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_REF_JITTER2</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_REF_JITTER2" spirit:order="135" spirit:configGroups="0 NoDisplay">0.010</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_STARTUP_WAIT</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_STARTUP_WAIT" spirit:order="136" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT0_DIVIDE_F</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT0_DIVIDE_F" spirit:order="137" spirit:configGroups="0 NoDisplay">10</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT0_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT0_DUTY_CYCLE" spirit:order="138" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT0_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT0_PHASE" spirit:order="139" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT0_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT0_USE_FINE_PS" spirit:order="140" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT1_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT1_DIVIDE" spirit:order="141" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT1_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT1_DUTY_CYCLE" spirit:order="142" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT1_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT1_PHASE" spirit:order="143" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT1_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT1_USE_FINE_PS" spirit:order="144" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT2_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT2_DIVIDE" spirit:order="145" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT2_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT2_DUTY_CYCLE" spirit:order="146" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT2_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT2_PHASE" spirit:order="147" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT2_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT2_USE_FINE_PS" spirit:order="148" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT3_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT3_DIVIDE" spirit:order="149" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT3_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT3_DUTY_CYCLE" spirit:order="150" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT3_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT3_PHASE" spirit:order="151" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT3_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT3_USE_FINE_PS" spirit:order="152" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT4_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT4_DIVIDE" spirit:order="153" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT4_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT4_DUTY_CYCLE" spirit:order="154" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT4_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT4_PHASE" spirit:order="155" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT4_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT4_USE_FINE_PS" spirit:order="156" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT5_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT5_DIVIDE" spirit:order="157" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT5_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT5_DUTY_CYCLE" spirit:order="158" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT5_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT5_PHASE" spirit:order="159" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT5_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT5_USE_FINE_PS" spirit:order="160" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT6_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT6_DIVIDE" spirit:order="161" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT6_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT6_DUTY_CYCLE" spirit:order="162" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT6_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT6_PHASE" spirit:order="163" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MMCM_CLKOUT6_USE_FINE_PS</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.MMCM_CLKOUT6_USE_FINE_PS" spirit:order="164" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>OVERRIDE_PLL</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.OVERRIDE_PLL" spirit:order="165" spirit:configGroups="0 NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_NOTES</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_NOTES" spirit:order="166" spirit:configGroups="0 NoDisplay">None</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_BANDWIDTH</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_BANDWIDTH" spirit:choiceRef="choice_list_a9bdfce0" spirit:order="167" spirit:configGroups="0 NoDisplay">OPTIMIZED</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKFBOUT_MULT</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKFBOUT_MULT" spirit:order="168" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="64" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKFBOUT_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKFBOUT_PHASE" spirit:order="169" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLK_FEEDBACK</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLK_FEEDBACK" spirit:choiceRef="choice_list_b9d38208" spirit:order="170" spirit:configGroups="0 NoDisplay">CLKFBOUT</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_DIVCLK_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_DIVCLK_DIVIDE" spirit:order="171" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="52" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKIN_PERIOD</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKIN_PERIOD" spirit:order="172" spirit:configGroups="0 NoDisplay">10.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_COMPENSATION</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_COMPENSATION" spirit:choiceRef="choice_pairs_035ca1c3" spirit:order="173" spirit:configGroups="0 NoDisplay">SYSTEM_SYNCHRONOUS</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_REF_JITTER</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_REF_JITTER" spirit:order="174" spirit:configGroups="0 NoDisplay">0.010</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT0_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT0_DIVIDE" spirit:order="175" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT0_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT0_DUTY_CYCLE" spirit:order="176" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT0_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT0_PHASE" spirit:order="177" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT1_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT1_DIVIDE" spirit:order="178" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT1_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT1_DUTY_CYCLE" spirit:order="179" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT1_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT1_PHASE" spirit:order="180" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT2_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT2_DIVIDE" spirit:order="181" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT2_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT2_DUTY_CYCLE" spirit:order="182" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT2_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT2_PHASE" spirit:order="183" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT3_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT3_DIVIDE" spirit:order="184" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT3_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT3_DUTY_CYCLE" spirit:order="185" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT3_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT3_PHASE" spirit:order="186" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT4_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT4_DIVIDE" spirit:order="187" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT4_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT4_DUTY_CYCLE" spirit:order="188" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT4_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT4_PHASE" spirit:order="189" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT5_DIVIDE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT5_DIVIDE" spirit:order="190" spirit:configGroups="0 NoDisplay" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT5_DUTY_CYCLE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT5_DUTY_CYCLE" spirit:order="191" spirit:configGroups="0 NoDisplay">0.500</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PLL_CLKOUT5_PHASE</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.PLL_CLKOUT5_PHASE" spirit:order="192" spirit:configGroups="0 NoDisplay">0.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>RESET_TYPE</spirit:name>
      <spirit:displayName>Reset Type</spirit:displayName>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.RESET_TYPE" spirit:choiceRef="choice_pairs_a4fbc00c" spirit:order="408" spirit:configGroups="0 NoDisplay">ACTIVE_HIGH</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_SAFE_CLOCK_STARTUP</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_SAFE_CLOCK_STARTUP" spirit:order="85.5" spirit:configGroups="0; NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_CLOCK_SEQUENCING</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_CLOCK_SEQUENCING" spirit:order="501" spirit:configGroups="0; NoDisplay">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_SEQUENCE_NUMBER</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_SEQUENCE_NUMBER" spirit:order="502" spirit:configGroups="0; NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_SEQUENCE_NUMBER</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_SEQUENCE_NUMBER" spirit:order="503" spirit:configGroups="0; NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_SEQUENCE_NUMBER</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_SEQUENCE_NUMBER" spirit:order="504" spirit:configGroups="0; NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_SEQUENCE_NUMBER</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_SEQUENCE_NUMBER" spirit:order="505" spirit:configGroups="0; NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_SEQUENCE_NUMBER</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_SEQUENCE_NUMBER" spirit:order="506" spirit:configGroups="0; NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_SEQUENCE_NUMBER</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_SEQUENCE_NUMBER" spirit:order="507" spirit:configGroups="0; NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_SEQUENCE_NUMBER</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_SEQUENCE_NUMBER" spirit:order="508" spirit:configGroups="0; NoDisplay" spirit:minimum="1" spirit:maximum="7" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USE_BOARD_FLOW</spirit:name>
      <spirit:displayName>Generate Board based IO Constraints</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.USE_BOARD_FLOW" spirit:order="1.1">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_IN1_BOARD_INTERFACE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_IN1_BOARD_INTERFACE" spirit:choiceRef="choice_pairs_340369e0" spirit:order="13.8">Custom</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLK_IN2_BOARD_INTERFACE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CLK_IN2_BOARD_INTERFACE" spirit:choiceRef="choice_pairs_340369e0" spirit:order="13.9">Custom</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DIFF_CLK_IN1_BOARD_INTERFACE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DIFF_CLK_IN1_BOARD_INTERFACE" spirit:choiceRef="choice_pairs_0920eb1b" spirit:order="13.1">Custom</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>DIFF_CLK_IN2_BOARD_INTERFACE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.DIFF_CLK_IN2_BOARD_INTERFACE" spirit:choiceRef="choice_pairs_0920eb1b" spirit:order="13.2">Custom</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>AUTO_PRIMITIVE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.AUTO_PRIMITIVE" spirit:choiceRef="choice_pairs_77d3d587" spirit:order="13212">MMCM</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>RESET_BOARD_INTERFACE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.RESET_BOARD_INTERFACE" spirit:choiceRef="choice_list_ac75ef1e" spirit:order="21.4">Custom</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_CDDC</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_CDDC" spirit:order="509">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CDDCDONE_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CDDCDONE_PORT" spirit:order="510" spirit:configGroups="0 NoDisplay">cddcdone</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CDDCREQ_PORT</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CDDCREQ_PORT" spirit:order="511" spirit:configGroups="0 NoDisplay">cddcreq</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_CLKOUTPHY</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_CLKOUTPHY" spirit:order="123.1">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUTPHY_REQUESTED_FREQ</spirit:name>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUTPHY_REQUESTED_FREQ" spirit:order="123.2" spirit:configGroups="0 NoDisplay">600.000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_JITTER</spirit:name>
      <spirit:displayName>Clkout1 Jitter</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_JITTER" spirit:order="1000">140.709</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT1_PHASE_ERROR</spirit:name>
      <spirit:displayName>Clkout1 Phase</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT1_PHASE_ERROR" spirit:order="1001">105.461</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_JITTER</spirit:name>
      <spirit:displayName>Clkout2 Jitter</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_JITTER" spirit:order="1002">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT2_PHASE_ERROR</spirit:name>
      <spirit:displayName>Clkout2 Phase</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT2_PHASE_ERROR" spirit:order="1003">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_JITTER</spirit:name>
      <spirit:displayName>Clkout3 Jitter</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_JITTER" spirit:order="1004">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT3_PHASE_ERROR</spirit:name>
      <spirit:displayName>Clkout3 Phase</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT3_PHASE_ERROR" spirit:order="1005">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_JITTER</spirit:name>
      <spirit:displayName>Clkout4 Jitter</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_JITTER" spirit:order="1006">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT4_PHASE_ERROR</spirit:name>
      <spirit:displayName>Clkout4 Phase</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT4_PHASE_ERROR" spirit:order="1007">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_JITTER</spirit:name>
      <spirit:displayName>Clkout5 Jitter</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_JITTER" spirit:order="1008">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT5_PHASE_ERROR</spirit:name>
      <spirit:displayName>Clkout5 Phase</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT5_PHASE_ERROR" spirit:order="1009">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_JITTER</spirit:name>
      <spirit:displayName>Clkout6 Jitter</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_JITTER" spirit:order="1010">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT6_PHASE_ERROR</spirit:name>
      <spirit:displayName>Clkout6 Phase</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT6_PHASE_ERROR" spirit:order="1011">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_JITTER</spirit:name>
      <spirit:displayName>Clkout7 Jitter</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_JITTER" spirit:order="1012">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CLKOUT7_PHASE_ERROR</spirit:name>
      <spirit:displayName>Clkout7 Phase</spirit:displayName>
      <spirit:value spirit:format="float" spirit:resolve="user" spirit:id="PARAM_VALUE.CLKOUT7_PHASE_ERROR" spirit:order="1013">0.0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>INPUT_MODE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.INPUT_MODE" spirit:choiceRef="choice_pairs_f669c2f5" spirit:order="7.8">frequency</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>INTERFACE_SELECTION</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.INTERFACE_SELECTION" spirit:choiceRef="choice_pairs_8b28f1f7" spirit:order="11.1">Enable_AXI</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>AXI_DRP</spirit:name>
      <spirit:displayName>Write DRP registers</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.AXI_DRP" spirit:order="11.12">false</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PHASE_DUTY_CONFIG</spirit:name>
      <spirit:displayName>Phase Duty Cycle Config</spirit:displayName>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.PHASE_DUTY_CONFIG" spirit:order="11.2">false</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>Clocking Wizard</xilinx:displayName>
      <xilinx:xpmLibraries>
        <xilinx:xpmLibrary>XPM_CDC</xilinx:xpmLibrary>
      </xilinx:xpmLibraries>
      <xilinx:coreRevision>2</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.ADDR_WIDTH" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.ARUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.AWUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.BUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.DATA_WIDTH" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_BRESP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_BURST" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_CACHE" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_LOCK" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_PROT" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_QOS" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_REGION" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_RRESP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.HAS_WSTRB" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.ID_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.PROTOCOL" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.RUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXI_LITE.WUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT1_DRIVES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT1_JITTER" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT1_PHASE_ERROR" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT1_REQUESTED_OUT_FREQ" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT2_DRIVES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT3_DRIVES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT4_DRIVES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT5_DRIVES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT6_DRIVES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CLKOUT7_DRIVES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.MMCM_CLKFBOUT_MULT_F" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.MMCM_CLKOUT0_DIVIDE_F" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.MMCM_COMPENSATION" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.MMCM_DIVCLK_DIVIDE" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.PRIMITIVE" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2018.3</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="busInterfaces" xilinx:value="efefe28e"/>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="44e36c51"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="4f3d3737"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="fcf089ff"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="96115976"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
