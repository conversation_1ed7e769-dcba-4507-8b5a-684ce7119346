#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Mon Jul 21 09:03:01 2025
# Process ID: 30240
# Current directory: H:/lab2/proj_miniRV_ego1/proj_single_cycle
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent5992 H:\lab2\proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr
# Log file: H:/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.log
# Journal file: H:/lab2/proj_miniRV_ego1/proj_single_cycle\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr
update_compile_order -fileset sources_1
create_ip -name clk_wiz -vendor xilinx.com -library ip -version 6.0 -module_name cpuclk -dir h:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip
set_property -dict [list CONFIG.Component_Name {cpuclk}] [get_ips cpuclk]
generate_target {instantiation_template} [get_files h:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/ip/cpuclk/cpuclk.xci]
update_compile_order -fileset sources_1
