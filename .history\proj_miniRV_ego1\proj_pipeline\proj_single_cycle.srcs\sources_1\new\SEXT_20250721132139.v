`timescale 1ns / 1ps
`include "defines.vh"

module SEXT (
    input  wire [2:0]  op,    // 符号扩展操作码
    input  wire [31:7] din,   // 输入数据（指令字段）
    output reg  [31:0] ext    // 符号扩展结果
);

    // 符号扩展逻辑
    always @(*) begin
        case (op)
            `SEXT_I: begin  // I型立即数扩展
                ext = {{20{din[31]}}, din[31:20]};
            end
            `SEXT_S: begin  // S型立即数扩展
                ext = {{20{din[31]}}, din[31:25], din[11:7]};
            end
            `SEXT_B: begin  // B型立即数扩展
                ext = {{19{din[31]}}, din[31], din[7], din[30:25], din[11:8], 1'b0};
            end
            `SEXT_U: begin  // U型立即数扩展
                ext = {din[31:12], 12'h000};
            end
            `SEXT_J: begin  // J型立即数扩展
                ext = {{11{din[31]}}, din[31], din[19:12], din[20], din[30:21], 1'b0};
            end
            default: begin
                ext = 32'h0;
            end
        endcase
    end
endmodule
